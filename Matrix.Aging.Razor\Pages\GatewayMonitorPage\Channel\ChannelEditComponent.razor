@namespace Matrix.Aging.Razor
@using Matrix.Admin.Application
@using Matrix.Admin.Razor
@using Matrix.Foundation
@using Matrix.Aging.Application
@inherits ComponentDefault

<div class="channel">

    @if (ValidateEnable)
    {
        <ValidateForm Model="Model" OnValidSubmit="ValidSubmit">
            @renderFragment
            <div class="form-footer">
                <Button ButtonType="ButtonType.Submit" Icon="fa-solid fa-floppy-disk" IsAsync Text=@RazorLocalizer["Save"] />
            </div>
        </ValidateForm>
    }
    else
    {
        @renderFragment
    }
</div>


@code {
    RenderFragment renderFragment =>
@<EditorForm class="p-2" AutoGenerateAllItem="false" RowType=RowType.Inline ItemsPerRow=2 LabelWidth=200 Model="Model">
    <FieldItems>
        <EditorItem TValue="string" TModel="Channel" @bind-Field="@context.Name">
            <EditTemplate Context="value">
                <div class="col-12">
                    <h6>@_Localizer["BasicInformation"]</h6>
                </div>
            </EditTemplate>
        </EditorItem>

        <EditorItem @bind-Field="@context.Name" Readonly=BatchEditEnable />

        <EditorItem @bind-Field="@context.PluginName">
            <EditTemplate Context="value">
                <div class="col-12  col-md-6">
                    <Select @bind-Value="@value.PluginName"
                            Items="@PluginNames" IsDisabled=BatchEditEnable
                            ShowSearch="true">
                        <ItemTemplate Context="name">
                            @if (PluginDcit.TryGetValue(name.Value, out var pluginOutput))
                                                        {
                            <span>@name.Text</span>
                                                        }
                                                        else
                            {
                                <span>@name.Value</span>
                            }
                        </ItemTemplate>
                    </Select>
                </div>
            </EditTemplate>
        </EditorItem>

        <EditorItem @bind-Field="@context.Enable" />
        <EditorItem @bind-Field="@context.LogLevel" />

        <EditorItem @bind-Field="@context.IsAging" />

        <EditorItem TValue="string" TModel="Channel" @bind-Field="@context.Name">
            <EditTemplate Context="value">
                <div class="col-12">
                    <h6>@_Localizer["Connection"]</h6>
                </div>
            </EditTemplate>
        </EditorItem>

        <EditorItem @bind-Field="@context.ChannelType">
            <EditTemplate Context="value">
                <div class="col-12 col-sm-6 col-md-6">
                    <Select SkipValidate="true" @bind-Value="@value.ChannelType" OnSelectedItemChanged=@((a)=>
                                                                                                             {
                                                                                                                 return InvokeAsync(StateHasChanged);
                                                                                                             }) />
                </div>
            </EditTemplate>

        </EditorItem>


        <EditorItem @bind-Field="@context.RemoteUrl" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpClient && context.ChannelType != ChannelTypeEnum.UdpSession) />
        <EditorItem @bind-Field="@context.BindUrl" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpClient && context.ChannelType != ChannelTypeEnum.UdpSession && context.ChannelType != ChannelTypeEnum.TcpService) />
        
        <EditorItem @bind-Field="@context.PortName" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.BaudRate" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.DataBits" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.Parity" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.StopBits" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.DtrEnable" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        <EditorItem @bind-Field="@context.RtsEnable" Ignore=@(context.ChannelType != ChannelTypeEnum.SerialPort) />
        
        <EditorItem @bind-Field="@context.CacheTimeout" Ignore=@(context.ChannelType == ChannelTypeEnum.UdpSession || context.ChannelType == ChannelTypeEnum.Other || context.ChannelType == ChannelTypeEnum.CAN) />
        <EditorItem @bind-Field="@context.ConnectTimeout" Ignore=@(context.ChannelType == ChannelTypeEnum.UdpSession || context.ChannelType == ChannelTypeEnum.TcpService || context.ChannelType == ChannelTypeEnum.Other) />
        <EditorItem @bind-Field="@context.MaxConcurrentCount" Ignore=@(context.ChannelType == ChannelTypeEnum.Other || context.ChannelType == ChannelTypeEnum.CAN) />
        
        <EditorItem @bind-Field="@context.CanType" Ignore=@(context.ChannelType != ChannelTypeEnum.CAN) />
        <EditorItem @bind-Field="@context.CanBaudRate" Ignore=@(context.ChannelType != ChannelTypeEnum.CAN) />
        <EditorItem @bind-Field="@context.IsCanFd" Ignore=@(context.ChannelType != ChannelTypeEnum.CAN) />
        <EditorItem @bind-Field="@context.CanSet" Ignore=@(context.ChannelType != ChannelTypeEnum.CAN) />
        
        <EditorItem @bind-Field="@context.CanBmsId" Ignore=@(context.ChannelType != ChannelTypeEnum.CAN)>
            <EditTemplate Context="value">
                <div class="col-12 col-md-6">
                    <BootstrapInputGroup>
                        <Select IsVirtualize 
                                DefaultVirtualizeItemText=@(GetCanBmsDisplayText(value.CanBmsId)) 
                                @bind-Value="@value.CanBmsId" 
                                IsDisabled=BatchEditEnable 
                                Items="@CanBmsItems" 
                                ShowSearch="true" 
                                ShowLabel="true"
                                PlaceHolder="@_Localizer["SelectCanBmsProtocol"]" />
                        <Button IsDisabled=BatchEditEnable 
                                class="text-end" 
                                Icon="fa-solid fa-plus" 
                                OnClick="AddCanBms"
                                Title="@_Localizer["AddCanBms"]"></Button>
                    </BootstrapInputGroup>
                </div>
            </EditTemplate>
        </EditorItem>
        
        <EditorItem @bind-Field="@context.MaxClientCount" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpService) />
        <EditorItem @bind-Field="@context.CheckClearTime" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpService) />
        <EditorItem @bind-Field="@context.Heartbeat" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpService && context.ChannelType != ChannelTypeEnum.TcpClient && context.ChannelType != ChannelTypeEnum.UdpSession) />
        <EditorItem @bind-Field="@context.HeartbeatTime" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpClient && context.ChannelType != ChannelTypeEnum.UdpSession) />
        <EditorItem @bind-Field="@context.DtuId" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpClient && context.ChannelType != ChannelTypeEnum.UdpSession) />
        <EditorItem @bind-Field="@context.DtuSeviceType" Ignore=@(context.ChannelType != ChannelTypeEnum.TcpService && context.ChannelType != ChannelTypeEnum.UdpSession) />

    </FieldItems>

</EditorForm>;

}
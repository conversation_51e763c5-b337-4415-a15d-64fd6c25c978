using System;
using Matrix.Foundation;

namespace Matrix.Tests
{
    /// <summary>
    /// 以太网CAN解析测试程序
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 以太网CAN解析测试 ===");

            // 测试数据1：88 18 FF 22 F4 30 75 F4 7E 00 00 00 00
            TestSingleFrame1();

            // 测试数据2：88 18 90 22 F4 E8 01 00 00 30 75 00 00
            TestSingleFrame2();

            // 测试构建数据包
            TestBuildPacket();

            // 测试多帧数据流
            TestMultipleFrames();

            Console.WriteLine("=== 测试完成 ===");
            Console.ReadKey();
        }

        static void TestSingleFrame1()
        {
            Console.WriteLine("\n--- 测试帧1 ---");
            // 根据新格式：FF 22 F4 30 75 F4 7E 00 00 00 00 00 00
            // 帧信息: FF, CAN ID: 22 F4 30 75, 数据: F4 7E 00 00 00 00 00 00
            var testData = new byte[] { 0xFF, 0x22, 0xF4, 0x30, 0x75, 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

            Console.WriteLine($"原始数据: {string.Join(" ", Array.ConvertAll(testData, b => b.ToString("X2")))}");

            try
            {
                var frame = CanUtility.ParseEthernetCanPacket(testData);
                Console.WriteLine($"解析结果:");
                Console.WriteLine($"  帧信息: 0x{testData[0]:X2}");
                Console.WriteLine($"  CAN ID: 0x{frame.Id:X8}");
                Console.WriteLine($"  扩展帧: {frame.IsExtended}");
                Console.WriteLine($"  远程帧: {frame.IsRemote}");
                Console.WriteLine($"  数据长度: {frame.Data?.Length ?? 0}");
                if (frame.Data != null && frame.Data.Length > 0)
                {
                    Console.WriteLine($"  数据: {string.Join(" ", Array.ConvertAll(frame.Data, b => b.ToString("X2")))}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析失败: {ex.Message}");
            }
        }

        static void TestSingleFrame2()
        {
            Console.WriteLine("\n--- 测试帧2 ---");
            // 根据新格式：90 22 F4 E8 01 00 00 30 75 00 00 00 00
            // 帧信息: 90, CAN ID: 22 F4 E8 01, 数据: 00 00 30 75 00 00 00 00
            var testData = new byte[] { 0x90, 0x22, 0xF4, 0xE8, 0x01, 0x00, 0x00, 0x30, 0x75, 0x00, 0x00, 0x00, 0x00 };

            Console.WriteLine($"原始数据: {string.Join(" ", Array.ConvertAll(testData, b => b.ToString("X2")))}");

            try
            {
                var frame = CanUtility.ParseEthernetCanPacket(testData);
                Console.WriteLine($"解析结果:");
                Console.WriteLine($"  帧信息: 0x{testData[0]:X2}");
                Console.WriteLine($"  CAN ID: 0x{frame.Id:X8}");
                Console.WriteLine($"  扩展帧: {frame.IsExtended}");
                Console.WriteLine($"  远程帧: {frame.IsRemote}");
                Console.WriteLine($"  数据长度: {frame.Data?.Length ?? 0}");
                if (frame.Data != null && frame.Data.Length > 0)
                {
                    Console.WriteLine($"  数据: {string.Join(" ", Array.ConvertAll(frame.Data, b => b.ToString("X2")))}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析失败: {ex.Message}");
            }
        }

        static void TestBuildPacket()
        {
            Console.WriteLine("\n--- 测试构建数据包 ---");

            try
            {
                var canId = 0x22F43075u;
                var canData = new byte[] { 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00 };
                var frame = new CanFrame(canId, canData);

                Console.WriteLine($"输入CAN帧:");
                Console.WriteLine($"  CAN ID: 0x{frame.Id:X8}");
                Console.WriteLine($"  扩展帧: {frame.IsExtended}");
                Console.WriteLine($"  数据: {string.Join(" ", Array.ConvertAll(frame.Data, b => b.ToString("X2")))}");

                var packet = CanUtility.BuildEthernetCanPacket(frame);
                Console.WriteLine($"构建的数据包({packet.Length}字节): {string.Join(" ", Array.ConvertAll(packet, b => b.ToString("X2")))}");

                // 验证往返转换
                var parsedFrame = CanUtility.ParseEthernetCanPacket(packet);
                Console.WriteLine($"往返转换验证:");
                Console.WriteLine($"  CAN ID: 0x{parsedFrame.Id:X8} (匹配: {parsedFrame.Id == frame.Id})");
                Console.WriteLine($"  扩展帧: {parsedFrame.IsExtended} (匹配: {parsedFrame.IsExtended == frame.IsExtended})");
                Console.WriteLine($"  数据长度: {parsedFrame.Data?.Length ?? 0}");
                if (parsedFrame.Data != null && parsedFrame.Data.Length > 0)
                {
                    Console.WriteLine($"  解析数据: {string.Join(" ", Array.ConvertAll(parsedFrame.Data, b => b.ToString("X2")))}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"构建/解析失败: {ex.Message}");
            }
        }

        static void TestMultipleFrames()
        {
            Console.WriteLine("\n--- 测试多帧数据流 ---");

            // 模拟您提供的原始数据流（去掉88 18前缀）
            var multiFrameData = new byte[]
            {
                // 第一帧：FF 22 F4 30 75 F4 7E 00 00 00 00 00 00
                0xFF, 0x22, 0xF4, 0x30, 0x75, 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                // 第二帧：90 22 F4 E8 01 00 00 30 75 00 00 00 00
                0x90, 0x22, 0xF4, 0xE8, 0x01, 0x00, 0x00, 0x30, 0x75, 0x00, 0x00, 0x00, 0x00
            };

            Console.WriteLine($"多帧数据流({multiFrameData.Length}字节): {string.Join(" ", Array.ConvertAll(multiFrameData, b => b.ToString("X2")))}");

            try
            {
                // 解析第一帧
                var frame1Data = new byte[13];
                Array.Copy(multiFrameData, 0, frame1Data, 0, 13);
                var frame1 = CanUtility.ParseEthernetCanPacket(frame1Data);

                Console.WriteLine($"\n第一帧解析结果:");
                Console.WriteLine($"  帧信息: 0x{frame1Data[0]:X2}");
                Console.WriteLine($"  CAN ID: 0x{frame1.Id:X8}");
                Console.WriteLine($"  数据: {string.Join(" ", Array.ConvertAll(frame1.Data, b => b.ToString("X2")))}");

                // 解析第二帧
                var frame2Data = new byte[13];
                Array.Copy(multiFrameData, 13, frame2Data, 0, 13);
                var frame2 = CanUtility.ParseEthernetCanPacket(frame2Data);

                Console.WriteLine($"\n第二帧解析结果:");
                Console.WriteLine($"  帧信息: 0x{frame2Data[0]:X2}");
                Console.WriteLine($"  CAN ID: 0x{frame2.Id:X8}");
                Console.WriteLine($"  数据: {string.Join(" ", Array.ConvertAll(frame2.Data, b => b.ToString("X2")))}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"多帧解析失败: {ex.Message}");
            }
        }
    }
}

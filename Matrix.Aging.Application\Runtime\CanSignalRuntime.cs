namespace Matrix.Aging.Application;

/// <summary>
/// CAN信号运行时（用于表格显示的展平数据）
/// </summary>
public class CanSignalRuntime
{
    /// <summary>
    /// 信号ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 设备ID（向后兼容）
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称（向后兼容）
    /// </summary>
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// BMS名称
    /// </summary>
    public string BmsName { get; set; } = string.Empty;

    /// <summary>
    /// 报文ID
    /// </summary>
    public uint MessageId { get; set; }

    /// <summary>
    /// 接收报文ID（向后兼容，与MessageId相同）
    /// </summary>
    public uint ReceiveMessageId => MessageId;

    /// <summary>
    /// 报文名称
    /// </summary>
    public string MessageName { get; set; } = string.Empty;

    /// <summary>
    /// 信号名称
    /// </summary>
    public string SignalName { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 解析方式（Intel或Motorola）
    /// </summary>
    public CanByteOrderEnum ByteOrder { get; set; } = CanByteOrderEnum.Intel;

    /// <summary>
    /// 起始位
    /// </summary>
    public ushort StartBit { get; set; }

    /// <summary>
    /// 位长度
    /// </summary>
    public byte BitLength { get; set; } = 1;

    /// <summary>
    /// 分辨率
    /// </summary>
    public double Resolution { get; set; } = 1.0;

    /// <summary>
    /// 偏移量
    /// </summary>
    public double Offset { get; set; } = 0.0;

    /// <summary>
    /// 是否为有符号信号
    /// </summary>
    public bool IsSigned { get; set; } = false;

    /// <summary>
    /// 多包设置
    /// </summary>
    public string? MultiPacketConfig { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 初始值
    /// </summary>
    public double? InitialValue { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool IsSave { get; set; } = true;

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool IsDisplay { get; set; } = true;

    /// <summary>
    /// 使能
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    /// 当前值（运行时值）
    /// </summary>
    public double CurrentValue { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastReceiveTime { get; set; }

    /// <summary>
    /// 原始CanSignal对象
    /// </summary>
    public CanSignal CanSignal { get; set; } = null!;

    /// <summary>
    /// 原始CanMessageRuntime对象
    /// </summary>
    public CanMessageRuntime CanMessageRuntime { get; set; } = null!;

    /// <summary>
    /// 信号在Message中的索引
    /// </summary>
    public int SignalIndex { get; set; }

    /// <summary>
    /// 从CanBmsRuntime创建CanSignalRuntime列表
    /// </summary>
    /// <param name="canBmsRuntimes">CAN BMS运行时列表</param>
    /// <returns>CAN信号运行时列表</returns>
    public static List<CanSignalRuntime> FromCanBmsRuntimes(IEnumerable<CanBmsRuntime> canBmsRuntimes)
    {
        var signalRuntimes = new List<CanSignalRuntime>();

        foreach (var bmsRuntime in canBmsRuntimes)
        {
            foreach (var messageRuntime in bmsRuntime.MessageRuntimes.Values)
            {
                for (int i = 0; i < messageRuntime.Signals.Length; i++)
                {
                    var signal = messageRuntime.Signals[i];
                    var currentValue = i < messageRuntime.SignalValues.Length ? messageRuntime.SignalValues[i] : 0.0;
                    var isOnline = i < messageRuntime.SignalOnlineStatus.Length ? messageRuntime.SignalOnlineStatus[i] : false;

                    var signalRuntime = new CanSignalRuntime
                    {
                        Id = signal.Id,
                        DeviceId = 0, // 默认值，需要从外部设置
                        DeviceName = string.Empty, // 默认值，需要从外部设置
                        BmsName = bmsRuntime.CanBms.BmsName,
                        MessageId = messageRuntime.CanMessage.MessageId,
                        MessageName = messageRuntime.CanMessage.MessageName,
                        SignalName = signal.SignalName,
                        Description = signal.Description,
                        ByteOrder = signal.ByteOrder,
                        StartBit = signal.StartBit,
                        BitLength = signal.BitLength,
                        Resolution = signal.Resolution,
                        Offset = signal.Offset,
                        IsSigned = signal.IsSigned,
                        MultiPacketConfig = signal.MultiPacketConfig,
                        Unit = signal.Unit,
                        InitialValue = signal.InitialValue,
                        IsSave = signal.IsSave,
                        IsDisplay = signal.IsDisplay,
                        Enable = signal.Enable,
                        CurrentValue = currentValue,
                        IsOnline = isOnline,
                        LastReceiveTime = messageRuntime.LastReceiveTime,
                        CanSignal = signal,
                        CanMessageRuntime = messageRuntime,
                        SignalIndex = i
                    };

                    signalRuntimes.Add(signalRuntime);
                }
            }
        }

        return signalRuntimes;
    }

    /// <summary>
    /// 从CanBmsRuntime创建CanSignalRuntime列表（包含设备信息）
    /// </summary>
    /// <param name="canBmsRuntimes">CAN BMS运行时列表</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="deviceName">设备名称</param>
    /// <returns>CAN信号运行时列表</returns>
    public static List<CanSignalRuntime> FromCanBmsRuntimes(IEnumerable<CanBmsRuntime> canBmsRuntimes, long deviceId, string deviceName)
    {
        var signalRuntimes = FromCanBmsRuntimes(canBmsRuntimes);
        
        // 设置设备信息
        foreach (var signalRuntime in signalRuntimes)
        {
            signalRuntime.DeviceId = deviceId;
            signalRuntime.DeviceName = deviceName;
        }
        
        return signalRuntimes;
    }

    /// <summary>
    /// 从CanDeviceRuntime创建CanSignalRuntime列表
    /// </summary>
    /// <param name="deviceRuntime">CAN设备运行时</param>
    /// <returns>CAN信号运行时列表</returns>
    public static List<CanSignalRuntime> FromCanDeviceRuntime(CanDeviceRuntime deviceRuntime)
    {
        return FromCanBmsRuntimes(deviceRuntime.BmsRuntimes, deviceRuntime.DeviceId, deviceRuntime.DeviceName);
    }
}

<Project>

	<!--插件直接加载到程序上下文，不隔离-->

	<!--<ItemGroup>
		--><!--Modbus 插件--><!--
		<PackageReference Include="Matrix.PowerPlugin.Modbus" Version="$(PluginVersion)" GeneratePathProperty="true">
			<Private>false</Private>
			<IncludeAssets> native;</IncludeAssets>
		</PackageReference>
	</ItemGroup>-->

	<ItemGroup>
		<ProjectReference Include="..\PowerPlugin\Matrix.PowerPlugin.Modbus\Matrix.PowerPlugin.Modbus.csproj" />
		<ProjectReference Include="..\PowerPlugin\Matrix.PowerPlugin.HXPower\Matrix.PowerPlugin.HXPower.csproj" />
	</ItemGroup>

	<Target Name="CopyPluginNugetPackages" AfterTargets="Build">
		<PropertyGroup>
			<PluginFolder>$(TargetDir)Plugins\</PluginFolder>
			<MatrixPluginFolder>$(TargetDir)MatrixPlugins\</MatrixPluginFolder>
		</PropertyGroup>

		<RemoveDir Directories="$(PluginFolder)" />
		<RemoveDir Directories="$(MatrixPluginFolder)" />
		<PropertyGroup>
			<PluginTargetFramework>net8.0</PluginTargetFramework>
		</PropertyGroup>
		<ItemGroup>
			 <!--setting up the variable for convenience--> 
			<PkgMatrix_PowerPlugin_ModbusPackageFiles Include="$(PkgMatrix_PowerPlugin_Modbus)\Content\$(PluginTargetFramework)\**\*.*" />
			<PkgMatrix_PowerPlugin_HXPowerPackageFiles Include="$(PkgMatrix_PowerPlugin_HXPower)\Content\$(PluginTargetFramework)\**\*.*" />
		</ItemGroup>

		<Message Text="将插件复制到插件目录 $(PluginFolder)" Importance="high" />
		<Copy SourceFiles="@(PkgMatrix_PowerPlugin_ModbusPackageFiles)" DestinationFolder="$(PluginFolder)%(RecursiveDir)" />
		<Copy SourceFiles="@(PkgMatrix_PowerPlugin_HXPowerPackageFiles)" DestinationFolder="$(PluginFolder)%(RecursiveDir)" />

	</Target>

</Project>

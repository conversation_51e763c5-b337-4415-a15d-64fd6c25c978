namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanSignal种子数据
/// </summary>
public class HXPowerCanSignalSeedData : ISqlSugarEntitySeedData<CanSignal>
{
    /// <inheritdoc/>
    public IEnumerable<CanSignal> SeedData()
    {
        var signals = new List<CanSignal>();

        // 为每个数据报文创建信号
        for (int module = 1; module <= 10; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                var dataMessageId = 20000 + (module - 1) * 4 + (channel - 1) * 2 + 1;
                var controlMessageId = 20000 + (module - 1) * 4 + (channel - 1) * 2 + 2;

                // 数据报文信号
                // 工作模式（第1字节）
                signals.Add(new CanSignal
                {
                    Id = 40000 + (module - 1) * 10 + (channel - 1) * 5 + 1,
                    CanMessageId = dataMessageId,
                    SignalName = $"Module{module}_Channel{channel}_WorkMode",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 0,
                    BitLength = 8,
                    Resolution = 1.0,
                    Offset = 0.0,
                    IsSigned = false,
                    MaxValue = 255,
                    MinValue = 0,
                    Unit = "",
                    InitialValue = 0,
                    MultiPacketConfig = null,
                    IsSave = true,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}工作模式",
                    Enable = true,
                    DataType = DataTypeEnum.Byte
                });

                // 电压（第2-4字节，3字节，分辨率0.00001V）
                signals.Add(new CanSignal
                {
                    Id = 40000 + (module - 1) * 10 + (channel - 1) * 5 + 2,
                    CanMessageId = dataMessageId,
                    SignalName = $"Module{module}_Channel{channel}_Voltage",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 8,
                    BitLength = 24,
                    Resolution = 0.00001,
                    Offset = 0.0,
                    IsSigned = false,
                    MaxValue = 655.35,
                    MinValue = 0,
                    Unit = "V",
                    InitialValue = 0,
                    MultiPacketConfig = null,
                    IsSave = true,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}电压值",
                    Enable = true,
                    DataType = DataTypeEnum.Double
                });

                // 电流（第5-8字节，4字节，分辨率0.00001A，有符号）
                signals.Add(new CanSignal
                {
                    Id = 40000 + (module - 1) * 10 + (channel - 1) * 5 + 3,
                    CanMessageId = dataMessageId,
                    SignalName = $"Module{module}_Channel{channel}_Current",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 32,
                    BitLength = 32,
                    Resolution = 0.00001,
                    Offset = 0.0,
                    IsSigned = true,
                    MaxValue = 2000.0,
                    MinValue = -2000.0,
                    Unit = "A",
                    InitialValue = 0,
                    MultiPacketConfig = null,
                    IsSave = true,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}电流值",
                    Enable = true,
                    DataType = DataTypeEnum.Double
                });

                // 控制报文信号
                // 工作模式控制（第3字节）
                signals.Add(new CanSignal
                {
                    Id = 50000 + (module - 1) * 4 + (channel - 1) * 2 + 1,
                    CanMessageId = controlMessageId,
                    SignalName = $"Module{module}_Channel{channel}_WorkMode",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 16,
                    BitLength = 8,
                    Resolution = 1.0,
                    Offset = 0.0,
                    IsSigned = false,
                    MaxValue = 255,
                    MinValue = 0,
                    Unit = "",
                    InitialValue = 1,
                    MultiPacketConfig = null,
                    IsSave = false,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}工作模式控制",
                    Enable = true,
                    DataType = DataTypeEnum.Byte
                });

                // 控制值（第5-8字节，4字节，分辨率0.00001）
                signals.Add(new CanSignal
                {
                    Id = 50000 + (module - 1) * 4 + (channel - 1) * 2 + 2,
                    CanMessageId = controlMessageId,
                    SignalName = $"Module{module}_Channel{channel}_ControlValue",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 32,
                    BitLength = 32,
                    Resolution = 0.00001,
                    Offset = 0.0,
                    IsSigned = true,
                    MaxValue = 2000.0,
                    MinValue = -2000.0,
                    Unit = "",
                    InitialValue = 0,
                    MultiPacketConfig = null,
                    IsSave = false,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}控制值",
                    Enable = true,
                    DataType = DataTypeEnum.Double
                });
            }
        }

        // 心跳报文信号
        signals.Add(new CanSignal
        {
            Id = 60001,
            CanMessageId = 30001, // 心跳报文ID
            SignalName = "Heartbeat_Life",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 0,
            BitLength = 8,
            Resolution = 1.0,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "心跳生命帧",
            Enable = true,
            DataType = DataTypeEnum.Byte
        });

        // 故障报文信号
        for (int module = 1; module <= 10; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                var faultMessageId = 31000 + (module - 1) * 2 + (channel - 1);

                signals.Add(new CanSignal
                {
                    Id = 61000 + (module - 1) * 2 + (channel - 1),
                    CanMessageId = faultMessageId,
                    SignalName = $"Module{module}_Channel{channel}_FaultCode",
                    ByteOrder = CanByteOrderEnum.Intel,
                    StartBit = 0,
                    BitLength = 64,
                    Resolution = 1.0,
                    Offset = 0.0,
                    IsSigned = false,
                    MaxValue = ulong.MaxValue,
                    MinValue = 0,
                    Unit = "",
                    InitialValue = 0,
                    MultiPacketConfig = null,
                    IsSave = true,
                    IsDisplay = true,
                    Description = $"模块{module}通道{channel}故障代码",
                    Enable = true,
                    DataType = DataTypeEnum.UInt64
                });
            }
        }

        return signals;
    }
} 
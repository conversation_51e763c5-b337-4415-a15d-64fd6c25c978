using BootstrapBlazor.Components;
using Matrix.Foundation;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN发送报文表（管理CanBms的后台发送报文）
/// </summary>
[SugarTable("can_send_message", TableDescription = "CAN发送报文表")]
[Tenant(SqlSugarConst.DB_Custom)]
[SugarIndex("index_can_bms", nameof(CanSendMessage.CanBmsId), OrderByType.Asc)]
[SugarIndex("unique_send_message", nameof(CanSendMessage.CanBmsId), OrderByType.Asc, nameof(CanSendMessage.MessageId), OrderByType.Asc, true)]
public class CanSendMessage : BaseDataEntity, IValidatableObject
{
    /// <summary>
    /// 所属CanBms ID
    /// </summary>
    [SugarColumn(ColumnDescription = "CanBms ID")]
    [AutoGenerateColumn(Visible = true, Order = 1, Filterable = false, Sortable = false)]
    [Required]
    [NotNull]
    public virtual long CanBmsId { get; set; }

    /// <summary>
    /// 发送报文ID
    /// </summary>
    [SugarColumn(ColumnDescription = "发送报文ID")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 2)]
    [Required]
    public virtual uint MessageId { get; set; }

    /// <summary>
    /// 发送报文名称
    /// </summary>
    [SugarColumn(ColumnDescription = "发送报文名称", Length = 100, IsNullable = false)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 3)]
    [Required]
    public virtual string MessageName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? Description { get; set; }

    /// <summary>
    /// 是否启用发送
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用发送")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool SendEnable { get; set; } = true;

    /// <summary>
    /// 开始发送时间（毫秒，相对于Channel连接成功时间）
    /// </summary>
    [SugarColumn(ColumnDescription = "开始发送时间(ms)")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int SendStartDelayMs { get; set; } = 0;

    /// <summary>
    /// 发送间隔（毫秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送间隔(ms)")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int SendIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 发送次数（0为一直发送，大于0时按设置次数发送）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送次数")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int SendCount { get; set; } = 0;

    /// <summary>
    /// 发送数据类型（字节数据或信号值）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送数据类型")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual CanSendDataTypeEnum SendDataType { get; set; } = CanSendDataTypeEnum.ByteData;

    /// <summary>
    /// 发送字节数据（当SendDataType为ByteData时使用，格式：00 01 02 03 04 05 06 07）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送字节数据", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? SendByteData { get; set; }

    /// <summary>
    /// 发送信号配置JSON（当SendDataType为SignalData时使用，格式：{"信号名1": 值1, "信号名2": 值2}）
    /// </summary>
    [SugarColumn(IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, ColumnDescription = "发送信号配置JSON", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual Dictionary<string, double>? SendSignalData { get; set; }

    /// <summary>
    /// 是否为扩展帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否为扩展帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsExtended { get; set; } = false;

    /// <summary>
    /// 是否为远程帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否为远程帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsRemote { get; set; } = false;

    /// <summary>
    /// 是否为CAN FD帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否为CAN FD帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsCanFd { get; set; } = false;

    /// <summary>
    /// 优先级（数值越小优先级越高）
    /// </summary>
    [SugarColumn(ColumnDescription = "优先级")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int Priority { get; set; } = 100;

    /// <summary>
    /// 验证方法
    /// </summary>
    /// <param name="validationContext">验证上下文</param>
    /// <returns>验证结果</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // 验证发送间隔
        if (SendIntervalMs < 1)
        {
            results.Add(new ValidationResult("发送间隔必须大于0毫秒", new[] { nameof(SendIntervalMs) }));
        }

        // 验证发送次数
        if (SendCount < 0)
        {
            results.Add(new ValidationResult("发送次数不能为负数", new[] { nameof(SendCount) }));
        }

        // 验证发送数据
        if (SendDataType == CanSendDataTypeEnum.ByteData)
        {
            if (string.IsNullOrWhiteSpace(SendByteData))
            {
                results.Add(new ValidationResult("字节数据类型时，发送字节数据不能为空", new[] { nameof(SendByteData) }));
            }
            else
            {
                // 验证字节数据格式
                var bytes = SendByteData.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (bytes.Length == 0 || bytes.Length > 8)
                {
                    results.Add(new ValidationResult("字节数据长度必须在1-8字节之间", new[] { nameof(SendByteData) }));
                }
                else
                {
                    foreach (var byteStr in bytes)
                    {
                        if (!byte.TryParse(byteStr, System.Globalization.NumberStyles.HexNumber, null, out _))
                        {
                            results.Add(new ValidationResult($"字节数据格式错误：{byteStr}，应为十六进制格式", new[] { nameof(SendByteData) }));
                            break;
                        }
                    }
                }
            }
        }
        else if (SendDataType == CanSendDataTypeEnum.SignalData)
        {
            if (SendSignalData == null || SendSignalData.Count == 0)
            {
                results.Add(new ValidationResult("信号数据类型时，发送信号配置不能为空", new[] { nameof(SendSignalData) }));
            }
        }

        return results;
    }

    /// <summary>
    /// 解析字节数据为字节数组
    /// </summary>
    /// <returns>字节数组</returns>
    public byte[] ParseByteData()
    {
        if (string.IsNullOrWhiteSpace(SendByteData))
            return Array.Empty<byte>();

        var byteStrings = SendByteData.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var bytes = new List<byte>();

        foreach (var byteStr in byteStrings)
        {
            if (byte.TryParse(byteStr, System.Globalization.NumberStyles.HexNumber, null, out var b))
            {
                bytes.Add(b);
            }
        }

        return bytes.ToArray();
    }

    /// <summary>
    /// 转换为CAN发送数据
    /// </summary>
    /// <returns>CAN发送数据</returns>
    public CanSendData ToCanSendData()
    {
        return new CanSendData
        {
            CanId = MessageId,
            Data = ParseByteData(),
            IsExtended = IsExtended,
            IsRemote = IsRemote,
            IsCanFd = IsCanFd
        };
    }
}

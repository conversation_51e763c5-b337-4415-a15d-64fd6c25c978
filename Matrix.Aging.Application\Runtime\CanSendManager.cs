using Matrix.Foundation;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN发送管理器（管理后台发送线程）
/// </summary>
public class CanSendManager : IDisposable
{
    private readonly ILogger? _logger;
    private readonly ICanSender _canSender;
    private readonly ConcurrentDictionary<long, CanSendTask> _sendTasks = new();
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="canSender">CAN发送器</param>
    /// <param name="logger">日志记录器</param>
    public CanSendManager(ICanSender canSender, ILogger? logger = null)
    {
        _canSender = canSender ?? throw new ArgumentNullException(nameof(canSender));
        _logger = logger;
    }

    /// <summary>
    /// 启动BMS的后台发送任务
    /// </summary>
    /// <param name="bmsRuntime">BMS运行时</param>
    public void StartBmsSendTasks(CanBmsRuntime bmsRuntime)
    {
        if (_disposed || bmsRuntime.BackgroundSendMessages.Count == 0)
            return;

        foreach (var sendMessage in bmsRuntime.BackgroundSendMessages)
        {
            var taskKey = GetTaskKey(bmsRuntime.CanBms.Id, sendMessage.Id);
            
            if (!_sendTasks.ContainsKey(taskKey))
            {
                var sendTask = new CanSendTask(sendMessage, bmsRuntime, _canSender, _logger);
                _sendTasks[taskKey] = sendTask;
                
                // 启动发送任务
                _ = Task.Run(async () => await sendTask.RunAsync(_cancellationTokenSource.Token).ConfigureAwait(false));
                
                _logger?.LogInformation($"启动CAN后台发送任务: BMS={bmsRuntime.CanBms.BmsName}, Message={sendMessage.MessageName}, ID=0x{sendMessage.MessageId:X8}");
            }
        }
    }

    /// <summary>
    /// 停止BMS的后台发送任务
    /// </summary>
    /// <param name="bmsId">BMS ID</param>
    public void StopBmsSendTasks(long bmsId)
    {
        var tasksToRemove = _sendTasks.Where(kvp => GetBmsIdFromTaskKey(kvp.Key) == bmsId).ToList();
        
        foreach (var kvp in tasksToRemove)
        {
            if (_sendTasks.TryRemove(kvp.Key, out var sendTask))
            {
                sendTask.Stop();
                _logger?.LogInformation($"停止CAN后台发送任务: TaskKey={kvp.Key}");
            }
        }
    }

    /// <summary>
    /// 停止所有发送任务
    /// </summary>
    public void StopAllSendTasks()
    {
        _cancellationTokenSource.Cancel();
        
        foreach (var kvp in _sendTasks)
        {
            kvp.Value.Stop();
        }
        
        _sendTasks.Clear();
        _logger?.LogInformation("停止所有CAN后台发送任务");
    }

    /// <summary>
    /// 获取任务键
    /// </summary>
    /// <param name="bmsId">BMS ID</param>
    /// <param name="sendMessageId">发送报文ID</param>
    /// <returns>任务键</returns>
    private static long GetTaskKey(long bmsId, long sendMessageId)
    {
        return (bmsId << 32) | (sendMessageId & 0xFFFFFFFF);
    }

    /// <summary>
    /// 从任务键获取BMS ID
    /// </summary>
    /// <param name="taskKey">任务键</param>
    /// <returns>BMS ID</returns>
    private static long GetBmsIdFromTaskKey(long taskKey)
    {
        return taskKey >> 32;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopAllSendTasks();
            _cancellationTokenSource.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// CAN发送任务
/// </summary>
internal class CanSendTask
{
    private readonly CanSendMessage _sendMessage;
    private readonly CanBmsRuntime _bmsRuntime;
    private readonly ICanSender _canSender;
    private readonly ILogger? _logger;
    private readonly CancellationTokenSource _taskCancellationTokenSource = new();
    private int _sentCount = 0;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sendMessage">发送报文配置</param>
    /// <param name="bmsRuntime">BMS运行时</param>
    /// <param name="canSender">CAN发送器</param>
    /// <param name="logger">日志记录器</param>
    public CanSendTask(CanSendMessage sendMessage, CanBmsRuntime bmsRuntime, ICanSender canSender, ILogger? logger)
    {
        _sendMessage = sendMessage;
        _bmsRuntime = bmsRuntime;
        _canSender = canSender;
        _logger = logger;
    }

    /// <summary>
    /// 运行发送任务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task RunAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _taskCancellationTokenSource.Token);
            var token = combinedCts.Token;

            // 等待开始发送时间
            if (_sendMessage.SendStartDelayMs > 0)
            {
                await Task.Delay(_sendMessage.SendStartDelayMs, token).ConfigureAwait(false);
            }

            while (!token.IsCancellationRequested)
            {
                // 检查发送次数限制
                if (_sendMessage.SendCount > 0 && _sentCount >= _sendMessage.SendCount)
                {
                    _logger?.LogInformation($"CAN发送任务完成: {_sendMessage.MessageName}, 已发送{_sentCount}次");
                    break;
                }

                // 发送CAN报文
                await SendCanMessageAsync().ConfigureAwait(false);
                _sentCount++;

                // 等待发送间隔
                await Task.Delay(_sendMessage.SendIntervalMs, token).ConfigureAwait(false);
            }
        }
        catch (OperationCanceledException)
        {
            _logger?.LogDebug($"CAN发送任务被取消: {_sendMessage.MessageName}");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"CAN发送任务异常: {_sendMessage.MessageName}");
        }
    }

    /// <summary>
    /// 发送CAN报文
    /// </summary>
    private async Task SendCanMessageAsync()
    {
        try
        {
            if (_sendMessage.SendDataType == CanSendDataTypeEnum.ByteData)
            {
                // 字节数据发送
                var data = _sendMessage.ParseByteData();
                await _canSender.SendCanFrameAsync(_sendMessage.MessageId, data).ConfigureAwait(false);
            }
            else if (_sendMessage.SendDataType == CanSendDataTypeEnum.SignalData && _sendMessage.SendSignalData != null)
            {
                // 信号数据发送
                await _canSender.SendCanSignalAsync(_sendMessage.MessageId, _sendMessage.SendSignalData).ConfigureAwait(false);
            }

            _logger?.LogTrace($"CAN报文发送成功: {_sendMessage.MessageName}, ID=0x{_sendMessage.MessageId:X8}, 第{_sentCount + 1}次");
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, $"CAN报文发送失败: {_sendMessage.MessageName}, ID=0x{_sendMessage.MessageId:X8}");
        }
    }

    /// <summary>
    /// 停止发送任务
    /// </summary>
    public void Stop()
    {
        _taskCancellationTokenSource.Cancel();
    }
}

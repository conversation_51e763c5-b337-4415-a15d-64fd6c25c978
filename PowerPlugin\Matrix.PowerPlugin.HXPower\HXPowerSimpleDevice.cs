using Matrix.Foundation.HXPower;
using System.Collections.Concurrent;

namespace Matrix.PowerPlugin.HXPower;

/// <summary>
/// HXPower简化设备实现
/// </summary>
public class HXPowerSimpleDevice
{
    private readonly ConcurrentDictionary<string, HXPowerDataProcessor.HXPowerDeviceStatus> _deviceStates = new();
    private readonly ConcurrentDictionary<string, object> _deviceVariables = new();

    /// <summary>
    /// 模块数量
    /// </summary>
    public int ModuleCount { get; } = 10;

    /// <summary>
    /// 每个模块的通道数量
    /// </summary>
    public int ChannelsPerModule { get; } = 2;

    /// <summary>
    /// 设置模块工作模式和控制值
    /// </summary>
    public async Task<bool> SetModuleParametersAsync(int moduleNumber, int channelNumber, 
        HXPowerDataProcessor.HXPowerWorkMode workMode, double controlValue)
    {
        try
        {
            // 验证参数
            if (moduleNumber < 1 || moduleNumber > ModuleCount)
                return false;

            if (channelNumber < 1 || channelNumber > ChannelsPerModule)
                return false;

            // 更新设备变量
            var moduleChannel = $"Module{moduleNumber}Channel{channelNumber}";
            _deviceVariables[$"{moduleChannel}_TargetWorkMode"] = workMode;
            _deviceVariables[$"{moduleChannel}_TargetControlValue"] = controlValue;

            // 创建控制命令
            var command = new HXPowerDataProcessor.HXPowerControlCommand
            {
                ModuleNumber = moduleNumber,
                ChannelNumber = channelNumber,
                WorkMode = workMode,
                ControlValue = controlValue
            };

            // 生成控制命令数据
            var canId = command.GetCanId();
            var commandData = HXPowerDataProcessor.GenerateControlCommandData(command);

            // 模拟发送成功
            Console.WriteLine($"模拟发送控制命令: 模块{moduleNumber}通道{channelNumber}, 模式{workMode}, 值{controlValue:F5}");
            Console.WriteLine($"CAN ID: 0x{canId:X8}, Data: {BitConverter.ToString(commandData)}");
            
            // 记录发送的命令
            _deviceVariables[$"{moduleChannel}_LastSentCommand"] = command;
            _deviceVariables[$"{moduleChannel}_LastSentTime"] = DateTime.Now;
            
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置模块参数异常: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 设置恒流模式
    /// </summary>
    public async Task<bool> SetConstantCurrentAsync(int moduleNumber, int channelNumber, double currentValue)
    {
        return await SetModuleParametersAsync(moduleNumber, channelNumber, 
            HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent, currentValue);
    }

    /// <summary>
    /// 设置恒压模式
    /// </summary>
    public async Task<bool> SetConstantVoltageAsync(int moduleNumber, int channelNumber, double voltageValue)
    {
        return await SetModuleParametersAsync(moduleNumber, channelNumber, 
            HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage, voltageValue);
    }

    /// <summary>
    /// 设置恒功率模式
    /// </summary>
    public async Task<bool> SetConstantPowerAsync(int moduleNumber, int channelNumber, double powerValue)
    {
        return await SetModuleParametersAsync(moduleNumber, channelNumber, 
            HXPowerDataProcessor.HXPowerWorkMode.ConstantPower, powerValue);
    }

    /// <summary>
    /// 设置静置模式
    /// </summary>
    public async Task<bool> SetRestAsync(int moduleNumber, int channelNumber)
    {
        return await SetModuleParametersAsync(moduleNumber, channelNumber, 
            HXPowerDataProcessor.HXPowerWorkMode.Rest, 0.0);
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    public HXPowerDataProcessor.HXPowerDeviceStatus? GetDeviceStatus(int moduleNumber, int channelNumber)
    {
        var key = $"Module{moduleNumber}Channel{channelNumber}";
        return _deviceStates.TryGetValue(key, out var status) ? status : null;
    }

    /// <summary>
    /// 模拟接收设备状态数据
    /// </summary>
    public void SimulateReceiveData(int moduleNumber, int channelNumber, 
        HXPowerDataProcessor.HXPowerWorkMode workMode, double voltage, double current)
    {
        var deviceStatus = new HXPowerDataProcessor.HXPowerDeviceStatus
        {
            ModuleNumber = moduleNumber,
            ChannelNumber = channelNumber,
            DeviceStatus = workMode,
            Voltage = voltage,
            Current = current,
            LastUpdateTime = DateTime.Now
        };

        var key = $"Module{moduleNumber}Channel{channelNumber}";
        _deviceStates[key] = deviceStatus;
    }

    /// <summary>
    /// 获取所有设备状态
    /// </summary>
    public Dictionary<string, HXPowerDataProcessor.HXPowerDeviceStatus> GetAllDeviceStatus()
    {
        return new Dictionary<string, HXPowerDataProcessor.HXPowerDeviceStatus>(_deviceStates);
    }

    /// <summary>
    /// 获取设备变量值
    /// </summary>
    public T? GetDeviceVariable<T>(string variableName)
    {
        if (_deviceVariables.TryGetValue(variableName, out var value) && value is T result)
        {
            return result;
        }
        return default;
    }
} 
using Matrix.Aging.Application;
using Matrix.Foundation;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using TouchSocket.Core;

namespace Matrix.Foundation.CAN;

/// <summary>
/// Foundation.CAN基础插件
/// 实现基础CAN报文发送功能，通过采集插件的写方法接收发送请求
/// </summary>
public class CanCollectDriver : CollectFoundationBase, ICanSender
{
    private readonly ILogger<CanCollectDriver>? _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public CanCollectDriver()
    {
        _logger = LogMessage as ILogger<CanCollectDriver>;
    }

    /// <summary>
    /// 重写InitChannelAsync以添加CAN发送管理器
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected override async Task InitChannelAsync(IChannel? channel, CancellationToken cancellationToken)
    {
        await base.InitChannelAsync(channel, cancellationToken).ConfigureAwait(false);

        // 如果是CAN通道，添加发送管理器事件处理
        if (channel is CanChannel canChannel)
        {
            // 订阅Channel事件以管理发送器
            canChannel.Started.Add(OnCanChannelStarted);
            canChannel.Stoped.Add(OnCanChannelStopped);
        }
    }

    /// <summary>
    /// CAN通道连接成功事件处理
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="last">是否为最后一个处理器</param>
    /// <returns></returns>
    private async ValueTask<bool> OnCanChannelStarted(IClientChannel channel, bool last)
    {
        try
        {
            var canDeviceRuntime = GetCanDeviceRuntime();
            if (canDeviceRuntime != null)
            {
                // 启动CAN发送管理器
                canDeviceRuntime.StartSendManager(this);
                _logger?.LogInformation($"CAN发送管理器已启动: {DeviceName}");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"启动CAN发送管理器失败: {DeviceName}");
        }

        return true;
    }

    /// <summary>
    /// CAN通道断开连接事件处理
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="last">是否为最后一个处理器</param>
    /// <returns></returns>
    private async ValueTask<bool> OnCanChannelStopped(IClientChannel channel, bool last)
    {
        try
        {
            var canDeviceRuntime = GetCanDeviceRuntime();
            if (canDeviceRuntime != null)
            {
                // 停止CAN发送管理器
                canDeviceRuntime.StopSendManager();
                _logger?.LogInformation($"CAN发送管理器已停止: {DeviceName}");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"停止CAN发送管理器失败: {DeviceName}");
        }

        return true;
    }

    /// <summary>
    /// 设备属性
    /// </summary>
    public override CollectFoundationPropertyBase CollectProperties => new CanCollectProperty();

    /// <summary>
    /// 获取地址描述
    /// </summary>
    /// <returns>地址描述</returns>
    public override string GetAddressDescription()
    {
        return "CAN基础插件地址格式：\n" +
               "字节数据发送：0x1800F401,00 01 02 03 04 05 06 07\n" +
               "信号数据发送：0x1800F401,Signal1=123.45,Signal2=67.89";
    }

    /// <summary>
    /// 检查是否连接
    /// </summary>
    /// <returns>连接状态</returns>
    public override bool IsConnected()
    {
        return CurrentChannel?.DeviceThreadManage?.Channel?.Online ?? false;
    }



    /// <summary>
    /// 写入变量（CAN发送的主要入口）
    /// </summary>
    /// <param name="writeInfoLists">写入信息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>写入结果</returns>
    protected override async ValueTask<Dictionary<string, OperResult>> WriteValuesAsync(Dictionary<VariableRuntime, JToken> writeInfoLists, CancellationToken cancellationToken)
    {
        var results = new Dictionary<string, OperResult>();

        foreach (var kvp in writeInfoLists)
        {
            var variableRuntime = kvp.Key;
            var value = kvp.Value?.ToString();

            try
            {
                // 解析写入值为CAN发送请求
                var sendRequest = ParseWriteValue(value);
                if (sendRequest != null)
                {
                    OperResult sendResult;
                    
                    if (sendRequest.RequestType == CanSendRequestType.ByteData && sendRequest.ByteData != null)
                    {
                        // 字节数据发送
                        sendResult = await SendCanFrameAsync(sendRequest.CanId, sendRequest.ByteData);
                    }
                    else if (sendRequest.RequestType == CanSendRequestType.SignalData && sendRequest.SignalData != null)
                    {
                        // 信号数据发送
                        sendResult = await SendCanSignalAsync(sendRequest.CanId, sendRequest.SignalData);
                    }
                    else
                    {
                        sendResult = new OperResult("无效的CAN发送请求格式");
                    }

                    results[variableRuntime.Name] = sendResult;
                }
                else
                {
                    results[variableRuntime.Name] = new OperResult("CAN发送请求解析失败");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"CAN发送异常: {variableRuntime.Name}={value}");
                results[variableRuntime.Name] = new OperResult($"CAN发送异常: {ex.Message}");
            }
        }

        return results;
    }

    /// <summary>
    /// 发送CAN报文（字节数据方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">字节数据</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> SendCanFrameAsync(uint canId, byte[] data)
    {
        try
        {
            if (CurrentChannel?.DeviceThreadManage?.Channel is not CanChannel canChannel)
            {
                return new OperResult("当前通道不是CAN通道");
            }

            if (!canChannel.Online)
            {
                return new OperResult("CAN通道未连接");
            }

            var success = await canChannel.SendCanFrameAsync(canId, data);
            if (success)
            {
                _logger?.LogDebug($"CAN字节数据发送成功: ID=0x{canId:X8}, Data={data.ToHexString(' ')}");
                return OperResult.Success;
            }
            else
            {
                return new OperResult("CAN帧发送失败");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"CAN字节数据发送异常: ID=0x{canId:X8}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 发送CAN报文（信号值方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值字典（信号名 -> 信号值）</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> SendCanSignalAsync(uint canId, Dictionary<string, double> signalValues)
    {
        try
        {
            if (CurrentChannel?.DeviceThreadManage?.Channel is not CanChannel canChannel)
            {
                return new OperResult("当前通道不是CAN通道");
            }

            if (!canChannel.Online)
            {
                return new OperResult("CAN通道未连接");
            }

            // 这里需要通过CanBms运行时来构建信号数据
            // 暂时返回未实现的错误，需要在后续完善
            _logger?.LogWarning($"CAN信号数据发送暂未实现: ID=0x{canId:X8}, Signals={string.Join(",", signalValues.Select(kvp => $"{kvp.Key}={kvp.Value}"))}");
            return new OperResult("CAN信号数据发送功能暂未实现，请使用字节数据发送");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"CAN信号数据发送异常: ID=0x{canId:X8}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 解析写入值为CAN发送请求
    /// </summary>
    /// <param name="value">写入值</param>
    /// <returns>CAN发送请求</returns>
    private CanPluginSendRequest? ParseWriteValue(string? value)
    {
        if (value == null)
            return null;

        try
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            return CanPluginSendRequest.Parse(value);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, $"解析CAN发送请求失败: {value}");
            return null;
        }
    }

    /// <summary>
    /// 实现抽象方法：加载源读取配置（CAN插件不使用Variable系统）
    /// </summary>
    /// <param name="variableRuntimes">变量运行时列表</param>
    /// <returns></returns>
    protected override async Task<List<VariableSourceRead>> ProtectedLoadSourceReadAsync(List<VariableRuntime> variableRuntimes)
    {
        // CAN插件不使用Variable系统，返回空列表
        await Task.CompletedTask;
        return new List<VariableSourceRead>();
    }
}

/// <summary>
/// CAN采集属性
/// </summary>
public class CanCollectProperty : CollectFoundationPropertyBase
{
    /// <summary>
    /// CAN设备不需要重试，因为是被动接收
    /// </summary>
    public override int RetryCount { get; set; } = 0;

    /// <summary>
    /// CAN设备不需要并发读取
    /// </summary>
    public override int MaxConcurrentCount { get; set; } = 1;

    /// <summary>
    /// CAN设备重连间隔时间
    /// </summary>
    public override int ReIntervalTime { get; set; } = 5000;
}

using SqlSugar;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN报文服务实现
/// </summary>
internal sealed class CanMessageService : BaseService<CanMessage>, ICanMessageService
{
    /// <summary>
    /// 根据ID获取CAN报文
    /// </summary>
    /// <param name="id">报文ID</param>
    /// <returns>CAN报文</returns>
    public async Task<CanMessage?> GetCanMessageByIdAsync(long id)
    {
        using var db = GetDB();
        return await db.Queryable<CanMessage>().FirstAsync(x => x.Id == id).ConfigureAwait(false);
    }

    /// <summary>
    /// 根据CanBms ID获取所有CAN报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN报文列表</returns>
    public async Task<List<CanMessage>> GetCanMessagesByCanBmsIdAsync(long canBmsId)
    {
        using var db = GetDB();
        return await db.Queryable<CanMessage>().Where(x => x.CanBmsId == canBmsId).ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 根据报文ID获取CAN报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <param name="messageId">报文ID</param>
    /// <returns>CAN报文</returns>
    public async Task<CanMessage?> GetCanMessageByMessageIdAsync(long canBmsId, uint messageId)
    {
        using var db = GetDB();
        return await db.Queryable<CanMessage>()
            .Where(x => x.CanBmsId == canBmsId && x.MessageId == messageId)
            .FirstAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 创建CAN报文
    /// </summary>
    /// <param name="canMessage">CAN报文</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateCanMessageAsync(CanMessage canMessage)
    {
        using var db = GetDB();
        return await db.Insertable(canMessage).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 更新CAN报文
    /// </summary>
    /// <param name="canMessage">CAN报文</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateCanMessageAsync(CanMessage canMessage)
    {
        using var db = GetDB();
        return await db.Updateable(canMessage).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 删除CAN报文
    /// </summary>
    /// <param name="id">报文ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanMessageAsync(long id)
    {
        using var db = GetDB();
        return await db.Deleteable<CanMessage>().Where(x => x.Id == id).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 批量创建CAN报文
    /// </summary>
    /// <param name="canMessages">CAN报文列表</param>
    /// <returns>创建结果</returns>
    public async Task<bool> BatchCreateCanMessagesAsync(List<CanMessage> canMessages)
    {
        using var db = GetDB();
        return await db.Insertable(canMessages).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 获取启用发送的CAN报文列表
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN报文列表</returns>
    public async Task<List<CanMessage>> GetSendEnabledCanMessagesAsync(long canBmsId)
    {
        using var db = GetDB();
        return await db.Queryable<CanMessage>()
            .Where(x => x.CanBmsId == canBmsId && x.MessageType == CanMessageTypeEnum.Send && x.Enable)
            .ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 根据CanBms ID删除所有CAN报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanMessagesByCanBmsIdAsync(long canBmsId)
    {
        using var db = GetDB();
        return await db.Deleteable<CanMessage>().Where(x => x.CanBmsId == canBmsId).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }
} 
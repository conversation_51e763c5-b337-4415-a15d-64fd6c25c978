﻿
using System.IO.Ports;

using Matrix.NewLife;

namespace Matrix.Foundation;

public interface IChannelOptions
{

    /// <summary>
    /// 通道类型
    /// </summary>
    ChannelTypeEnum ChannelType { get; set; }

    #region 以太网

    /// <summary>
    /// 远程ip
    /// </summary>
    string RemoteUrl { get; set; }

    /// <summary>
    /// 本地绑定ip，分号分隔，例如：***********:502;***********:502，表示绑定***********:502和***********:502
    /// </summary>
    string BindUrl { get; set; }

    #endregion

    #region 串口

    /// <summary>
    /// COM
    /// </summary>
    string PortName { get; set; }

    /// <summary>
    /// 波特率
    /// </summary>
    int BaudRate { get; set; }

    /// <summary>
    /// 数据位
    /// </summary>
    int DataBits { get; set; }

    /// <summary>
    /// 校验位
    /// </summary>
    Parity Parity { get; set; }

    /// <summary>
    /// 停止位
    /// </summary>
    StopBits StopBits { get; set; }

    /// <summary>
    /// DtrEnable
    /// </summary>
    bool DtrEnable { get; set; }

    /// <summary>
    /// RtsEnable
    /// </summary>
    bool RtsEnable { get; set; }


    #endregion

    #region CAN
    /// <summary>
    /// CAN类型
    /// 根据系统，有不同启动方式:SocketCAN,EthernetCAN,ZlgCAN
    /// </summary>
    CanBusType CanType { get; set; }

    /// <summary>
    /// 是否启用CAN FD协议
    /// </summary>
    bool IsCanFd { get; set; }

    /// <summary>
    /// CAN总线速度
    /// </summary>
    CanBusSpeed CanBaudRate { get; set; }

    /// <summary>
    /// CAN设置 - 根据CanType有不同格式：
    /// SocketCAN: can0, can1, vcan0...
    /// EthernetCAN: GC212,***********0:23 (型号,IP:端口)
    /// ZlgCAN:
    ///   USBCAN: USBCAN2,0,0 或 USBCANFD-200U,0,0
    ///   CANET: CANETTCP,*************:4001 或 CANFDNET-400U-TCP,*************:4001
    ///   PCICAN: PCI9840,0,0 或 PCIECANFD-400U,0,0
    /// </summary>
    string CanSet { get; set; }

    /// <summary>
    /// 验证CAN配置的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    bool IsCanConfigValid();

    /// <summary>
    /// 获取解析后的CAN配置信息
    /// </summary>
    /// <returns>CAN配置信息</returns>
    CanConfigInfo? GetCanConfigInfo();

    /// <summary>
    /// CAN BMS ID（新架构 - 通道直接选择协议文件）
    /// </summary>
    long? CanBmsId { get; set; }
    #endregion

    /// <summary>
    /// 最大并发数量
    /// </summary>
    int MaxConcurrentCount { get; set; }

    /// <summary>
    /// 组包缓存时间
    /// </summary>
    int CacheTimeout { get; set; }

    /// <summary>
    /// 连接超时时间
    /// </summary>
    ushort ConnectTimeout { get; set; }

    /// <summary>
    /// 通道并发控制锁
    /// </summary>
    WaitLock WaitLock { get; }

    TouchSocketConfig Config { get; set; }

    /// <summary>
    /// 是否老化测试
    /// </summary>
    bool IsAging { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    int MaxClientCount { get; set; }

    /// <summary>
    /// 客户端连接滑动过期时间(TCP服务通道时)
    /// </summary>
    int CheckClearTime { get; set; }

    /// <summary>
    /// 心跳检测(utf8)
    /// </summary>
    string Heartbeat { get; set; }

    #region dtu终端
    /// <summary>
    /// 心跳时间
    /// </summary>
    public int HeartbeatTime { get; set; }

    /// <summary>
    /// 默认Dtu注册包(utf-8)
    /// </summary>
    public string DtuId { get; set; }

    #endregion
    public DtuSeviceType DtuSeviceType { get; set; }
}

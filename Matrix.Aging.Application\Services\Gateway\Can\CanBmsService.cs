using SqlSugar;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN BMS服务实现
/// </summary>
internal sealed class CanBmsService : BaseService<CanBms>, ICanBmsService
{
    /// <summary>
    /// 根据ID获取CAN BMS
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>CAN BMS</returns>
    public async Task<CanBms?> GetCanBmsByIdAsync(long id)
    {
        using var db = GetDB();
        return await db.Queryable<CanBms>().FirstAsync(x => x.Id == id).ConfigureAwait(false);
    }

    /// <summary>
    /// 根据ID获取CAN BMS（同步方法）
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>CAN BMS</returns>
    public CanBms? GetCanBmsById(long id)
    {
        using var db = GetDB();
        return db.Queryable<CanBms>().First(x => x.Id == id);
    }

    /// <summary>
    /// 获取所有CAN BMS
    /// </summary>
    /// <returns>CAN BMS列表</returns>
    public async Task<List<CanBms>> GetAllCanBmsAsync()
    {
        using var db = GetDB();
        return await db.Queryable<CanBms>().ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 根据名称获取CAN BMS
    /// </summary>
    /// <param name="bmsName">BMS名称</param>
    /// <returns>CAN BMS</returns>
    public async Task<CanBms?> GetCanBmsByNameAsync(string bmsName)
    {
        using var db = GetDB();
        return await db.Queryable<CanBms>()
            .Where(x => x.BmsName == bmsName)
            .FirstAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 创建CAN BMS
    /// </summary>
    /// <param name="canBms">CAN BMS</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateCanBmsAsync(CanBms canBms)
    {
        using var db = GetDB();
        return await db.Insertable(canBms).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 更新CAN BMS
    /// </summary>
    /// <param name="canBms">CAN BMS</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateCanBmsAsync(CanBms canBms)
    {
        using var db = GetDB();
        return await db.Updateable(canBms).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 删除CAN BMS
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanBmsAsync(long id)
    {
        using var db = GetDB();
        return await db.Deleteable<CanBms>().Where(x => x.Id == id).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 批量创建CAN BMS
    /// </summary>
    /// <param name="canBmsList">CAN BMS列表</param>
    /// <returns>创建结果</returns>
    public async Task<bool> BatchCreateCanBmsAsync(List<CanBms> canBmsList)
    {
        using var db = GetDB();
        return await db.Insertable(canBmsList).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }


}

﻿using Mapster;

using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json.Linq;

using System.Collections.Concurrent;

using Matrix.Extension.Generic;
using Matrix.Aging.Application.Extensions;
using Matrix.NewLife.Json.Extension;
using Matrix.NewLife.Threading;
using Matrix.Foundation;

using TouchSocket.Core;

namespace Matrix.Aging.Application;

/// <summary>
/// <para></para>
/// 采集插件，继承实现不同PLC通讯
/// <para></para>
/// </summary>
public abstract class CollectBase : DriverBase
{
    /// <summary>
    /// 插件配置项
    /// </summary>
    public abstract CollectPropertyBase CollectProperties { get; }

    /// <summary>
    /// 特殊方法
    /// </summary>
    public List<DriverMethodInfo>? DriverMethodInfos { get; private set; }

    public sealed override object DriverProperties => CollectProperties;

    protected IStringLocalizer Localizer { get; set; }

    /// <summary>
    /// CAN设备运行时（仅当通道类型为CAN时使用）
    /// </summary>
    private CanDeviceRuntime? _canDeviceRuntime;

    /// <summary>
    /// CAN通道引用（仅当通道类型为CAN时使用）
    /// </summary>
    private CanChannel? _canChannel;

    public override async Task AfterVariablesChangedAsync(CancellationToken cancellationToken)
    {
        LogMessage?.LogInformation("Refresh variable");
        var currentDevice = CurrentDevice;
        IdVariableRuntimes.Clear();
        IdVariableRuntimes.AddRange(currentDevice.VariableRuntimes.Where(a => a.Value.Enable).ToDictionary(a => a.Value.Id, a => a.Value));

        // 如果是CAN通道，使用CAN专用的变量处理逻辑
        if (CurrentChannel?.ChannelType == ChannelTypeEnum.CAN)
        {
            await AfterCanVariablesChangedAsync(cancellationToken).ConfigureAwait(false);
            return;
        }

        //预热脚本，加速编译
        IdVariableRuntimes.Where(a => !string.IsNullOrWhiteSpace(a.Value.ReadExpressions))
         .Select(b => b.Value.ReadExpressions).ToHashSet().ParallelForEach(script =>
         {
             try
             {
                 _ = ExpressionEvaluatorExtension.GetOrAddScript(script);
             }
             catch
             {
             }
         });

        try
        {
            // 连读打包
            // 从收集的变量运行时信息中筛选需要读取的变量
            var tags = IdVariableRuntimes.Select(a => a.Value)
                .Where(it => it.ProtectType != ProtectTypeEnum.WriteOnly
                && string.IsNullOrEmpty(it.OtherMethod)
                && !string.IsNullOrEmpty(it.RegisterAddress));

            //筛选特殊变量地址
            //1、DeviceStatus
            Func<VariableRuntime, bool> source = (a =>
            {
                return !a.RegisterAddress.Equals(nameof(DeviceRuntime.DeviceStatus), StringComparison.OrdinalIgnoreCase) &&
                !a.RegisterAddress.Equals("Script", StringComparison.OrdinalIgnoreCase) &&
                !a.RegisterAddress.Equals("ScriptRead", StringComparison.OrdinalIgnoreCase)
                ;

            });

            currentDevice.VariableScriptReads = tags.Where(a => !source(a)).Select(a =>
            {
                var data = new VariableScriptRead();
                data.VariableRuntime = a;
                data.TimeTick = new(a.IntervalTime ?? currentDevice.IntervalTime);
                return data;
            }).ToList();

            // 将打包后的结果存储在当前设备的 VariableSourceReads 属性中
            currentDevice.VariableSourceReads = await ProtectedLoadSourceReadAsync(tags.Where(source).ToList()).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            // 如果出现异常，记录日志并初始化 VariableSourceReads 属性为新实例
            currentDevice.VariableSourceReads = new();
            LogMessage.LogWarning(ex, Localizer["VariablePackError", ex.Message]);
        }
        try
        {
            // 初始化动态方法
            var variablesMethod = IdVariableRuntimes.Select(a => a.Value).Where(it => !string.IsNullOrEmpty(it.OtherMethod));

            // 处理可读的动态方法
            {
                var tag = variablesMethod.Where(it => it.ProtectType != ProtectTypeEnum.WriteOnly);
                List<VariableMethod> variablesMethodResult = GetMethod(tag);
                currentDevice.ReadVariableMethods = variablesMethodResult;
            }

            // 处理可写的动态方法
            {
                var tag = variablesMethod.Where(it => it.ProtectType != ProtectTypeEnum.ReadOnly);
                currentDevice.MethodVariableCount = tag.Count();
            }
        }
        catch (Exception ex)
        {
            // 如果出现异常，记录日志并初始化 ReadVariableMethods 和 VariableMethods 属性为新实例
            currentDevice.ReadVariableMethods ??= new();
            LogMessage.LogWarning(ex, Localizer["GetMethodError", ex.Message]);
        }

        // 根据标签获取方法信息的局部函数
        List<VariableMethod> GetMethod(IEnumerable<VariableRuntime> tag)
        {
            var variablesMethodResult = new List<VariableMethod>();
            foreach (var item in tag)
            {
                // 根据标签查找对应的方法信息
                var method = DriverMethodInfos.FirstOrDefault(it => it.Name == item.OtherMethod);
                if (method != null)
                {
                    // 构建 VariableMethod 对象
                    var methodResult = new VariableMethod(new Method(method.MethodInfo), item, string.IsNullOrWhiteSpace(item.IntervalTime) ? item.DeviceRuntime.IntervalTime : item.IntervalTime);
                    variablesMethodResult.Add(methodResult);
                }
                else
                {
                    // 如果找不到对应方法，抛出异常
                    throw new(Localizer["MethodNotNull", item.Name, item.OtherMethod]);
                }
            }
            return variablesMethodResult;
        }
    }

    /// <summary>
    /// CAN通道专用的变量变更处理（纯CAN模式，最小化Variable处理）
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected virtual async Task AfterCanVariablesChangedAsync(CancellationToken cancellationToken)
    {
        try
        {
            LogMessage?.LogInformation("CAN模式：初始化配置（不使用Variable系统）");
            var currentDevice = CurrentDevice;

            // CAN模式完全基于CanBms配置，不使用Variable系统
            // 清空所有Variable相关的配置
            currentDevice.VariableSourceReads = new();
            currentDevice.VariableScriptReads = new();
            currentDevice.ReadVariableMethods = new();

            LogMessage?.LogInformation("CAN模式配置完成：使用纯CanBms配置，不依赖Variable系统");
        }
        catch (Exception ex)
        {
            LogMessage?.LogWarning(ex, $"CAN模式配置失败: {ex.Message}");
            // 确保清空Variable相关配置
            CurrentDevice.VariableSourceReads = new();
            CurrentDevice.VariableScriptReads = new();
            CurrentDevice.ReadVariableMethods = new();
        }
    }

    internal override void ProtectedInitDevice(DeviceRuntime device)
    {
        // 调用基类的初始化方法
        base.ProtectedInitDevice(device);
        Localizer = App.CreateLocalizerByType(typeof(CollectBase))!;

        // 从插件服务中获取当前设备关联的驱动方法信息列表
        DriverMethodInfos = GlobalData.PluginService.GetDriverMethodInfos(device.PluginName, this);
    }

    /// <summary>
    /// 重写InitChannelAsync以支持CAN通道
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected internal override async Task InitChannelAsync(IChannel? channel, CancellationToken cancellationToken)
    {
        await base.InitChannelAsync(channel, cancellationToken).ConfigureAwait(false);

        // 如果是CAN通道，初始化CAN功能
        if (channel?.ChannelType == ChannelTypeEnum.CAN && channel is CanChannel canChannel)
        {
            _canChannel = canChannel;

            // 初始化CAN设备运行时
            await InitializeCanDeviceRuntimeAsync().ConfigureAwait(false);

            // 注册CAN帧处理器
            _canChannel.RegisterCanFrameProcessor(ProcessCanFrame);

            // CAN设备需要主动连接通道（因为不会通过发送数据触发自动连接）
            if (!_canChannel.Online)
            {
                LogMessage?.LogInformation($"正在连接CAN通道: {_canChannel}");
                await _canChannel.ConnectAsync(_canChannel.ChannelOptions.ConnectTimeout, cancellationToken).ConfigureAwait(false);
                LogMessage?.LogInformation($"CAN通道连接成功: {_canChannel}");
            }

            LogMessage?.LogInformation($"CAN设备 {DeviceName} 初始化完成");
        }
    }

    public virtual string GetAddressDescription()
    {
        return string.Empty;
    }

    /// <summary>
    /// 初始化CAN设备运行时
    /// </summary>
    /// <returns></returns>
    private async Task InitializeCanDeviceRuntimeAsync()
    {
        try
        {
            // 创建CAN设备运行时
            _canDeviceRuntime = new CanDeviceRuntime(CurrentDevice.Id, DeviceName);

            // 获取服务
            var canBmsService = App.GetService<ICanBmsService>();
            var canMessageService = App.GetService<ICanMessageService>();
            var canSignalService = App.GetService<ICanSignalService>();

            if (canBmsService == null || canMessageService == null || canSignalService == null)
            {
                throw new InvalidOperationException("无法获取CAN相关服务");
            }

            // 获取Channel关联的CAN BMS配置
            var canBmsList = new List<CanBms>();
            if (_canChannel?.ChannelOptions.CanBmsId != null)
            {
                var canBms = await canBmsService.GetCanBmsByIdAsync(_canChannel.ChannelOptions.CanBmsId.Value).ConfigureAwait(false);
                if (canBms != null)
                {
                    canBmsList.Add(canBms);
                }
            }

            // 初始化运行时
            await _canDeviceRuntime.InitializeAsync(canBmsList, canMessageService, canSignalService).ConfigureAwait(false);

            LogMessage?.LogInformation($"CAN设备运行时初始化完成，BMS数量: {canBmsList.Count}");
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"初始化CAN设备运行时失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 处理CAN帧数据（纯CAN模式，不更新Variable）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>是否处理成功</returns>
    private bool ProcessCanFrame(uint canId, ReadOnlySpan<byte> canData, DateTime timestamp)
    {
        try
        {
            if (_canDeviceRuntime != null)
            {
                // 通过CanDeviceRuntime处理接收到的CAN帧
                // 数据直接解析到CanBms的信号中，不转换到Variable系统
                var success = _canDeviceRuntime.ProcessCanFrame(canId, canData.ToArray(), timestamp);

                // 更新设备状态
                CurrentDevice.SetDeviceStatus(timestamp, !success, success ? null : "CAN帧解析失败");

                if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                {
                    var signalCount = _canDeviceRuntime.GetSignalValuesByCanId(canId)?.Count ?? 0;
                    LogMessage?.Trace($"CAN帧处理: ID=0x{canId:X8}, 数据={string.Join(" ", canData.ToArray().Select(b => b.ToString("X2")))}, 成功={success}, 信号数={signalCount}");
                }

                return success;
            }
            return false;
        }
        catch (Exception ex)
        {
            LogMessage?.LogWarning($"处理CAN帧失败: ID=0x{canId:X8}, 错误={ex.Message}");
            CurrentDevice.SetDeviceStatus(timestamp, true, ex.Message);
            return false;
        }
    }
    /// <summary>
    /// 循环任务
    /// </summary>
    /// <param name="cancellationToken">取消操作的令牌。</param>
    /// <returns>表示异步操作结果的枚举。</returns>
    internal override async ValueTask<ThreadRunReturnTypeEnum> ExecuteAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 如果取消操作被请求，则返回中断状态
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 如果标志为停止，则暂停执行
            if (Pause)
            {
                // 暂停
                return ThreadRunReturnTypeEnum.Continue;
            }

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 获取设备连接状态并更新设备活动时间
            if (IsConnected())
            {
                CurrentDevice.SetDeviceStatus(TimerX.Now);
            }

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            //如果是CAN通讯，使用CAN专用处理逻辑
            if (CurrentChannel.ChannelType == ChannelTypeEnum.CAN)
            {
                // CAN通讯采用被动接收模式，不需要主动读取
                // 数据通过CAN帧处理器自动更新到变量中
                await ProtectedExecuteCanAsync(cancellationToken).ConfigureAwait(false);
            }
            else
            {
                // 非CAN通讯使用传统的变量打包和主动读取模式
                await ProtectedExecuteAsync(cancellationToken).ConfigureAwait(false);
            }

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 正常返回None状态
            return ThreadRunReturnTypeEnum.None;
        }
        catch (OperationCanceledException)
        {
            return ThreadRunReturnTypeEnum.Break;
        }
        catch (ObjectDisposedException)
        {
            return ThreadRunReturnTypeEnum.Break;
        }
        catch (Exception ex)
        {
            if (cancellationToken.IsCancellationRequested)
                return ThreadRunReturnTypeEnum.Break;
            // 记录异常信息，并更新设备状态为异常
            LogMessage?.LogError(ex, "Execute");
            CurrentDevice.SetDeviceStatus(TimerX.Now, true, ex.Message);
            return ThreadRunReturnTypeEnum.None;
        }
    }

    /// <summary>
    /// CAN通讯专用执行方法（纯CAN模式，不处理Variable）
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected virtual async ValueTask ProtectedExecuteCanAsync(CancellationToken cancellationToken)
    {
        try
        {
            // CAN通讯采用被动接收模式
            // 数据通过ProcessCanFrame自动处理，这里只需要：
            // 1. 检查CAN设备运行时状态
            // 2. 处理脚本变量（如果有的话）

            if (_canDeviceRuntime != null)
            {
                var now = DateTime.Now;

                // 检查CAN设备状态
                var statistics = _canDeviceRuntime.GetStatistics();
                if (statistics != null)
                {
                    // 根据最后通讯时间判断设备状态
                    var isOffline = (now - statistics.LastCommunicationTime).TotalSeconds > 5; // 5秒超时
                    CurrentDevice.SetDeviceStatus(now, isOffline, isOffline ? "CAN通讯超时" : null);

                    if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Debug)
                    {
                        LogMessage?.Debug($"CAN设备状态检查: 在线={!isOffline}, 最后通讯={statistics.LastCommunicationTime:HH:mm:ss.fff}");
                    }
                }
            }
        }
        finally
        {
            // 处理脚本变量（如果CAN设备配置了脚本变量）
            ScriptVariableRun(cancellationToken);
        }
    }

    /// <summary>
    /// 执行读取等方法，如果插件不支持读取，而是自更新值的话，需重写此方法
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected override async ValueTask ProtectedExecuteAsync(CancellationToken cancellationToken)
    {
        try
        {
            ReadResultCount readResultCount = new();
            if (cancellationToken.IsCancellationRequested)
                return;

            if (CollectProperties.MaxConcurrentCount > 1)
            {
                // 并行处理每个变量读取
                await CurrentDevice.VariableSourceReads.ParallelForEachAsync(async (variableSourceRead, cancellationToken) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;
                    if (await ReadVariableSource(readResultCount, variableSourceRead, cancellationToken).ConfigureAwait(false))
                        return;
                }
                , CollectProperties.MaxConcurrentCount, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                for (int i = 0; i < CurrentDevice.VariableSourceReads?.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;
                    if (await ReadVariableSource(readResultCount, CurrentDevice.VariableSourceReads[i], cancellationToken).ConfigureAwait(false))
                        return;
                }
            }

            if (CollectProperties.MaxConcurrentCount > 1)
            {
                // 并行处理每个方法调用
                await CurrentDevice.ReadVariableMethods.ParallelForEachAsync(async (readVariableMethods, cancellationToken) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;
                    if (await ReadVariableMed(readResultCount, readVariableMethods, cancellationToken).ConfigureAwait(false))
                        return;
                }
            , CollectProperties.MaxConcurrentCount, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                for (int i = 0; i < CurrentDevice.ReadVariableMethods.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;
                    if (await ReadVariableMed(readResultCount, CurrentDevice.ReadVariableMethods[i], cancellationToken).ConfigureAwait(false))
                        return;
                }
            }

            // 如果所有方法和变量读取都成功，则清零错误计数器
            if (readResultCount.deviceMethodsVariableFailedNum == 0 && readResultCount.deviceSourceVariableFailedNum == 0 && (readResultCount.deviceMethodsVariableSuccessNum != 0 || readResultCount.deviceSourceVariableSuccessNum != 0))
            {
                //只有成功读取一次，失败次数都会清零
                CurrentDevice.SetDeviceStatus(TimerX.Now, false);
            }
        }
        finally
        {
            ScriptVariableRun(cancellationToken);
        }

    }

    #region private

    #region 执行方法

    async ValueTask<bool> ReadVariableMed(ReadResultCount readResultCount, VariableMethod readVariableMethods, CancellationToken cancellationToken)
    {
        if (Pause)
            return true;
        if (cancellationToken.IsCancellationRequested)
            return true;

        // 如果请求更新时间已到，则执行方法调用
        if (readVariableMethods.CheckIfRequestAndUpdateTime())
        {
            if (cancellationToken.IsCancellationRequested)
                return true;
            if (cancellationToken.IsCancellationRequested)
                return true;
            if (await TestOnline(cancellationToken).ConfigureAwait(false))
                return true;
            var readErrorCount = 0;
            LogMessage?.Trace(string.Format("{0} - Executing method[{1}]", DeviceName, readVariableMethods.MethodInfo.Name));
            var readResult = await InvokeMethodAsync(readVariableMethods, cancellationToken: cancellationToken).ConfigureAwait(false);

            // 方法调用失败时重试一定次数
            while (!readResult.IsSuccess && readErrorCount < CollectProperties.RetryCount)
            {
                if (Pause)
                    return true;
                if (cancellationToken.IsCancellationRequested)
                    return true;
                if (await TestOnline(cancellationToken).ConfigureAwait(false))
                    return true;
                readErrorCount++;
                if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                    LogMessage?.Trace(string.Format("{0} - Execute method[{1}] - failed - {2}", DeviceName, readVariableMethods.MethodInfo.Name, readResult.ErrorMessage));

                LogMessage?.Trace(string.Format("{0} - Executing method[{1}]", DeviceName, readVariableMethods.MethodInfo.Name));
                readResult = await InvokeMethodAsync(readVariableMethods, cancellationToken: cancellationToken).ConfigureAwait(false);
            }

            if (readResult.IsSuccess)
            {
                // 方法调用成功时记录日志并增加成功计数器
                if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                    LogMessage?.Trace(string.Format("{0} - Execute method[{1}] - Succeeded {2}", DeviceName, readVariableMethods.MethodInfo.Name, readResult.Content?.ToJsonNetString()));
                readResultCount.deviceMethodsVariableSuccessNum++;
                CurrentDevice.SetDeviceStatus(TimerX.Now, false);
            }
            else
            {
                if (cancellationToken.IsCancellationRequested)
                    return true;

                // 方法调用失败时记录日志并增加失败计数器，更新错误信息
                if (readVariableMethods.LastErrorMessage != readResult.ErrorMessage)
                {
                    if (!cancellationToken.IsCancellationRequested)
                        LogMessage?.LogWarning(readResult.Exception, Localizer["MethodFail", DeviceName, readVariableMethods.MethodInfo.Name, readResult.ErrorMessage]);
                }
                else
                {
                    if (!cancellationToken.IsCancellationRequested)
                        if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                            LogMessage?.Trace(string.Format("{0} - Execute method[{1}] - failed - {2}", DeviceName, readVariableMethods.MethodInfo.Name, readResult.ErrorMessage));
                }

                readResultCount.deviceMethodsVariableFailedNum++;
                readVariableMethods.LastErrorMessage = readResult.ErrorMessage;
                CurrentDevice.SetDeviceStatus(TimerX.Now, false);
            }
        }

        return false;
    }

    #endregion

    #region 执行默认读取

    async ValueTask<bool> ReadVariableSource(ReadResultCount readResultCount, VariableSourceRead? variableSourceRead, CancellationToken cancellationToken)
    {
        if (Pause)
            return true;
        if (cancellationToken.IsCancellationRequested)
            return true;
        // 如果请求更新时间已到，则执行变量读取
        if (variableSourceRead.CheckIfRequestAndUpdateTime())
        {
            if (cancellationToken.IsCancellationRequested)
                return true;
            if (Pause)
                return true;
            if (await TestOnline(cancellationToken).ConfigureAwait(false))
                return true;

            var readErrorCount = 0;

            LogMessage?.Trace(string.Format("{0} - Collecting [{1} - {2}]", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length));
            var readResult = await ReadSourceAsync(variableSourceRead, cancellationToken).ConfigureAwait(false);

            // 读取失败时重试一定次数
            while (!readResult.IsSuccess && readErrorCount < CollectProperties.RetryCount)
            {
                if (Pause)
                    return true;
                if (cancellationToken.IsCancellationRequested)
                    return true;
                if (await TestOnline(cancellationToken).ConfigureAwait(false))
                    return true;
                readErrorCount++;
                if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                    LogMessage?.Trace(string.Format("{0} - Collection[{1} - {2}] failed - {3}", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length, readResult.ErrorMessage));


                LogMessage?.Trace(string.Format("{0} - Collecting [{1} - {2}]", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length));
                readResult = await ReadSourceAsync(variableSourceRead, cancellationToken).ConfigureAwait(false);
            }

            if (readResult.IsSuccess)
            {
                // 读取成功时记录日志并增加成功计数器
                if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                    LogMessage?.Trace(string.Format("{0} - Collection[{1} - {2}] data succeeded {3}", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length, readResult.Content?.ToHexString(' ')));
                readResultCount.deviceSourceVariableSuccessNum++;
                CurrentDevice.SetDeviceStatus(TimerX.Now, false);
            }
            else
            {
                {
                    if (cancellationToken.IsCancellationRequested)
                        return true;

                    // 读取失败时记录日志并增加失败计数器，更新错误信息并清除变量状态
                    if (variableSourceRead.LastErrorMessage != readResult.ErrorMessage)
                    {
                        if (!cancellationToken.IsCancellationRequested)
                            LogMessage?.LogWarning(readResult.Exception, Localizer["CollectFail", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length, readResult.ErrorMessage]);
                    }
                    else
                    {
                        if (!cancellationToken.IsCancellationRequested)
                            if (LogMessage.LogLevel <= TouchSocket.Core.LogLevel.Trace)
                                LogMessage?.Trace(string.Format("{0} - Collection[{1} - {2}] data failed - {3}", DeviceName, variableSourceRead?.RegisterAddress, variableSourceRead?.Length, readResult.ErrorMessage));
                    }

                    readResultCount.deviceSourceVariableFailedNum++;
                    variableSourceRead.LastErrorMessage = readResult.ErrorMessage;
                    CurrentDevice.SetDeviceStatus(TimerX.Now, true, readResult.ErrorMessage);
                    var time = DateTime.Now;
                    variableSourceRead.VariableRuntimes.ForEach(a => a.SetValue(null, time, isOnline: false));
                }
            }
        }

        return false;
    }

    #endregion

    #endregion

    protected virtual ValueTask<bool> TestOnline(CancellationToken cancellationToken)
    {
        return ValueTask.FromResult(false);
    }

    protected void ScriptVariableRun(CancellationToken cancellationToken)
    {
        DateTime dateTime = TimerX.Now;
        //特殊地址变量
        for (int i = 0; i < CurrentDevice.VariableScriptReads?.Count; i++)
        {
            if (cancellationToken.IsCancellationRequested)
                return;

            if (CurrentDevice.VariableScriptReads[i].CheckIfRequestAndUpdateTime())
            {

                var variableRuntime = CurrentDevice.VariableScriptReads[i].VariableRuntime;
                if (variableRuntime.RegisterAddress.Equals(nameof(DeviceRuntime.DeviceStatus), StringComparison.OrdinalIgnoreCase))
                {
                    variableRuntime.SetValue(variableRuntime.DeviceRuntime.DeviceStatus, dateTime);
                }
                else if (variableRuntime.RegisterAddress.Equals("ScriptRead", StringComparison.OrdinalIgnoreCase))
                {
                    variableRuntime.SetValue(variableRuntime.Value, dateTime);
                }

            }
        }
    }

    /// <summary>
    /// 连读打包，返回实际通讯包信息<see cref="VariableSourceRead"/>
    /// <br></br>每个驱动打包方法不一样，所以需要实现这个接口
    /// </summary>
    /// <param name="deviceVariables">设备下的全部通讯点位</param>
    /// <returns></returns>
    protected abstract Task<List<VariableSourceRead>> ProtectedLoadSourceReadAsync(List<VariableRuntime> deviceVariables);

    protected AsyncReadWriteLock ReadWriteLock = new();

    /// <summary>
    /// 采集驱动读取，读取成功后直接赋值变量
    /// </summary>
    protected virtual ValueTask<OperResult<byte[]>> ReadSourceAsync(VariableSourceRead variableSourceRead, CancellationToken cancellationToken)
    {
        return ValueTask.FromResult(new OperResult<byte[]>(new NotImplementedException()));
    }
    /// <summary>
    /// 批量写入变量值,需返回变量名称/结果
    /// </summary>
    /// <returns></returns>
    protected virtual ValueTask<Dictionary<string, OperResult>> WriteValuesAsync(Dictionary<VariableRuntime, JToken> writeInfoLists, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }


    private sealed class ReadResultCount
    {
        public int deviceMethodsVariableFailedNum = 0;
        public int deviceMethodsVariableSuccessNum = 0;
        public int deviceSourceVariableFailedNum = 0;
        public int deviceSourceVariableSuccessNum = 0;
    }

    #region 写入方法

    /// <summary>
    /// 异步写入方法
    /// </summary>
    /// <param name="writeInfoLists">要写入的变量及其对应的数据</param>
    /// <param name="cancellationToken">取消操作的通知</param>
    /// <returns>写入操作的结果字典</returns>
    internal async ValueTask<Dictionary<string, OperResult<object>>> InvokeMethodAsync(Dictionary<VariableRuntime, JToken> writeInfoLists, CancellationToken cancellationToken)
    {
        // 初始化结果字典
        Dictionary<string, OperResult<object>> results = new Dictionary<string, OperResult<object>>();

        // 遍历写入信息列表
        foreach (var (deviceVariable, jToken) in writeInfoLists)
        {
            // 检查是否有写入表达式
            if (!string.IsNullOrEmpty(deviceVariable.WriteExpressions))
            {
                // 提取原始数据
                object rawdata = jToken is JValue jValue ? jValue.Value : jToken is JArray jArray ? jArray : jToken.ToString();
                try
                {
                    // 根据写入表达式转换数据
                    object data = deviceVariable.WriteExpressions.GetExpressionsResult(rawdata, LogMessage);
                    // 将转换后的数据重新赋值给写入信息列表
                    writeInfoLists[deviceVariable] = JToken.FromObject(data);
                }
                catch (Exception ex)
                {
                    // 如果转换失败，则记录错误信息
                    results.Add(deviceVariable.Name, new OperResult<object>(Localizer["WriteExpressionsError", deviceVariable.Name, deviceVariable.WriteExpressions, ex.Message], ex));
                }
            }
        }

        ConcurrentDictionary<string, OperResult<object>> operResults = new();


        using var writeLock = ReadWriteLock.WriterLock();

        try
        {

            // 使用并发方式遍历写入信息列表，并进行异步写入操作
            await writeInfoLists
            .Where(a => !results.Any(b => b.Key == a.Key.Name))
            .ToDictionary(item => item.Key, item => item.Value).ParallelForEachAsync(async (writeInfo, cancellationToken) =>
        {
            try
            {
                // 调用协议的写入方法，将写入信息中的数据写入到对应的寄存器地址，并获取操作结果
                var result = await InvokeMethodAsync(writeInfo.Key.VariableMethod, writeInfo.Value?.ToString(), false, cancellationToken).ConfigureAwait(false);

                // 将操作结果添加到结果字典中，使用变量名称作为键
                operResults.TryAdd(writeInfo.Key.Name, result);
            }
            catch (Exception ex)
            {
                operResults.TryAdd(writeInfo.Key.Name, new(ex));
            }
        }, CollectProperties.MaxConcurrentCount, cancellationToken).ConfigureAwait(false);
        }
        finally
        {
        }

        // 将转换失败的变量和写入成功的变量的操作结果合并到结果字典中
        return results.Concat(operResults).ToDictionary(a => a.Key, a => a.Value);
    }

    /// <summary>
    /// 异步写入方法
    /// </summary>
    /// <param name="writeInfoLists">要写入的变量及其对应的数据</param>
    /// <param name="cancellationToken">取消操作的通知</param>
    /// <returns>写入操作的结果字典</returns>
    internal async ValueTask<Dictionary<string, OperResult>> InVokeWriteAsync(Dictionary<VariableRuntime, JToken> writeInfoLists, CancellationToken cancellationToken)
    {
        // 初始化结果字典
        Dictionary<string, OperResult> results = new Dictionary<string, OperResult>();


        // 遍历写入信息列表
        foreach (var (deviceVariable, jToken) in writeInfoLists)
        {
            // 检查是否有写入表达式
            if (!string.IsNullOrEmpty(deviceVariable.WriteExpressions))
            {
                // 提取原始数据
                object rawdata = jToken is JValue jValue ? jValue.Value : jToken is JArray jArray ? jArray : jToken.ToString();
                try
                {
                    // 根据写入表达式转换数据
                    object data = deviceVariable.WriteExpressions.GetExpressionsResult(rawdata, LogMessage);
                    // 将转换后的数据重新赋值给写入信息列表
                    writeInfoLists[deviceVariable] = JToken.FromObject(data);
                }
                catch (Exception ex)
                {
                    // 如果转换失败，则记录错误信息
                    results.Add(deviceVariable.Name, new OperResult(Localizer["WriteExpressionsError", deviceVariable.Name, deviceVariable.WriteExpressions, ex.Message], ex));
                }
            }
        }

        var writePList = writeInfoLists.Where(a => !CurrentDevice.VariableScriptReads.Select(a => a.VariableRuntime).Any(b => a.Key.Name == b.Name));
        var writeSList = writeInfoLists.Where(a => CurrentDevice.VariableScriptReads.Select(a => a.VariableRuntime).Any(b => a.Key.Name == b.Name));

        DateTime now = DateTime.Now;
        foreach (var item in writeSList)
        {
            results.TryAdd(item.Key.Name, item.Key.SetValue(item.Value, now));
        }

        // 过滤掉转换失败的变量，只保留写入成功的变量进行写入操作
        var results1 = await WriteValuesAsync(writePList
            .Where(a => !results.Any(b => b.Key == a.Key.Name))
            .ToDictionary(item => item.Key, item => item.Value),
            cancellationToken).ConfigureAwait(false);

        // 将转换失败的变量和写入成功的变量的操作结果合并到结果字典中
        return results.Concat(results1).ToDictionary(a => a.Key, a => a.Value);
    }

    /// <summary>
    /// 异步调用方法
    /// </summary>
    /// <param name="variableMethod">要调用的方法</param>
    /// <param name="value">传递给方法的参数值（可选）</param>
    /// <param name="isRead">指示是否为读取操作</param>
    /// <param name="cancellationToken">取消操作的通知</param>
    /// <returns>操作结果，包含执行方法的结果</returns>
    protected virtual async ValueTask<OperResult<object>> InvokeMethodAsync(VariableMethod variableMethod, string? value = null, bool isRead = true, CancellationToken cancellationToken = default)
    {
        try
        {
            // 初始化操作结果
            OperResult<object> result = new OperResult<object>();

            // 获取要执行的方法
            var method = variableMethod.MethodInfo;

            // 如果方法未找到，则返回错误结果
            if (method == null)
            {
                result.OperCode = 999;
                result.ErrorMessage = Localizer["MethodNotNull", variableMethod.Variable.Name, variableMethod.Variable.OtherMethod];
                return result;
            }
            else
            {
                // 调用方法并获取结果
                var data = await variableMethod.InvokeMethodAsync(this, value, cancellationToken).ConfigureAwait(false);
                result = data.Adapt<OperResult<object>>();

                // 如果方法有返回值，并且是读取操作
                if (method.HasReturn && isRead)
                {
                    var time = DateTime.Now;
                    if (result.IsSuccess == true)
                    {
                        // 将结果序列化并设置到变量中
                        var variableResult = variableMethod.Variable.SetValue(result.Content, time);
                        if (!variableResult.IsSuccess)
                            variableMethod.LastErrorMessage = result.ErrorMessage;
                    }
                    else
                    {
                        // 如果读取操作失败，则将变量标记为离线
                        var variableResult = variableMethod.Variable.SetValue(null, time, isOnline: false);
                        if (!variableResult.IsSuccess)
                            variableMethod.LastErrorMessage = result.ErrorMessage;
                    }
                }
                return result;
            }
        }
        catch (Exception ex)
        {
            // 捕获异常并返回错误结果
            return new OperResult<object>(ex);
        }
        finally
        {
        }
    }

    #endregion 写入方法

    #region CAN数据访问API

    /// <summary>
    /// 获取CAN信号值（按信号名称）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果不存在返回null</returns>
    public virtual double? GetCanSignalValue(string signalName)
    {
        if (_canDeviceRuntime == null)
        {
            LogMessage?.LogWarning("CAN设备运行时未初始化");
            return null;
        }

        try
        {
            // 遍历所有BMS查找信号
            foreach (var bmsRuntime in _canDeviceRuntime.BmsRuntimes)
            {
                var value = bmsRuntime.GetSignalValue(signalName);
                if (value.HasValue)
                {
                    return value.Value;
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            LogMessage?.LogWarning($"获取CAN信号值失败: {signalName}, 错误={ex.Message}");
            return null;
        }
    }



    /// <summary>
    /// 批量获取CAN信号值
    /// </summary>
    /// <param name="signalNames">信号名称列表</param>
    /// <returns>信号值字典</returns>
    public virtual Dictionary<string, double> GetCanSignalValues(params string[] signalNames)
    {
        var result = new Dictionary<string, double>();

        foreach (var signalName in signalNames)
        {
            var value = GetCanSignalValue(signalName);
            if (value.HasValue)
            {
                result[signalName] = value.Value;
            }
        }

        return result;
    }







    /// <summary>
    /// 获取CAN设备统计信息（仅当通道类型为CAN时可用）
    /// </summary>
    /// <returns>统计信息</returns>
    public virtual CanDeviceStatistics? GetCanStatistics()
    {
        return _canDeviceRuntime?.GetStatistics();
    }

    /// <summary>
    /// 获取所有CAN信号值（仅当通道类型为CAN时可用）
    /// </summary>
    /// <returns>信号值字典</returns>
    public virtual Dictionary<string, double> GetAllCanSignalValues()
    {
        return _canDeviceRuntime?.GetAllVariableValues() ?? new Dictionary<string, double>();
    }

    /// <summary>
    /// 检查是否为CAN通道
    /// </summary>
    /// <returns>是否为CAN通道</returns>
    public virtual bool IsCanChannel()
    {
        return CurrentChannel?.ChannelType == ChannelTypeEnum.CAN;
    }

    /// <summary>
    /// 获取CAN设备运行时（仅当通道类型为CAN时可用）
    /// </summary>
    /// <returns>CAN设备运行时</returns>
    public virtual CanDeviceRuntime? GetCanDeviceRuntime()
    {
        return _canDeviceRuntime;
    }

    #endregion CAN相关方法

    #region 资源清理

    /// <summary>
    /// 重写Dispose方法以清理CAN资源
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && IsCanChannel())
        {
            try
            {
                // 注销CAN帧处理器
                if (_canChannel != null)
                {
                    _canChannel.UnregisterCanFrameProcessor(ProcessCanFrame);
                }

                // 清理CAN设备运行时
                _canDeviceRuntime = null;
            }
            catch (Exception ex)
            {
                LogMessage?.LogWarning($"清理CAN资源时发生错误: {ex.Message}");
            }
        }

        base.Dispose(disposing);
    }

    #endregion 资源清理
}

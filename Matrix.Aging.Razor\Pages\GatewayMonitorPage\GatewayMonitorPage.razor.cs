﻿
using Matrix.Aging.Application;
using Matrix.Foundation;

namespace Matrix.Aging.Razor;

public partial class GatewayMonitorPage
{
    private ChannelDeviceTreeItem SelectModel { get; set; } = new() { ChannelDevicePluginType = ChannelDevicePluginTypeEnum.PluginType, PluginType = null };

    #region 查询


    private async Task TreeChangedAsync(ChannelDeviceTreeItem channelDeviceTreeItem)
    {
        ShowChannelRuntime = 0;
        ShowDeviceRuntime = 0;
        SelectModel = channelDeviceTreeItem;
        var variables = await GlobalData.GetCurrentUserIdVariables().ConfigureAwait(false);
        var channels = await GlobalData.GetCurrentUserChannels().ConfigureAwait(false);
        var devices = await GlobalData.GetCurrentUserDevices().ConfigureAwait(false);
        if (channelDeviceTreeItem.TryGetChannelRuntime(out var channelRuntime))
        {
            ShowChannelRuntime = channelRuntime.Id;

            // 检查是否为CAN通道，设置相应的数据
            if (channelRuntime.ChannelType == ChannelTypeEnum.CAN)
            {
                // CAN通道显示CanBms数据
                CanBmsRuntimes = await GetCanBmsRuntimesForChannelAsync(channelRuntime);
                VariableRuntimes = Enumerable.Empty<VariableRuntime>();
            }
            else
            {
                // 非CAN通道显示Variable数据
                if (channelRuntime.IsCollect == true)
                {
                    VariableRuntimes = channelRuntime.ReadDeviceRuntimes.SelectMany(a => a.Value.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null));
                }
                else
                {
                    VariableRuntimes = channelRuntime.ReadDeviceRuntimes.Where(a => a.Value?.Driver?.IdVariableRuntimes != null).SelectMany(a => a.Value?.Driver?.IdVariableRuntimes?.Where(a => a.Value != null)?.Select(a => a.Value)).Where(a => a != null);
                }
                CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
            }

            ChannelRuntimes = Enumerable.Repeat(channelRuntime, 1);
            DeviceRuntimes = channelRuntime.ReadDeviceRuntimes.Select(a => a.Value);
        }
        else if (channelDeviceTreeItem.TryGetDeviceRuntime(out var deviceRuntime))
        {
            ShowDeviceRuntime = deviceRuntime.Id;

            // 检查是否为CAN设备，设置相应的数据
            if (deviceRuntime.IsCanDevice())
            {
                // CAN设备显示CanBms数据
                CanBmsRuntimes = await GetCanBmsRuntimesForDeviceAsync(deviceRuntime);
                VariableRuntimes = Enumerable.Empty<VariableRuntime>();
            }
            else
            {
                // 非CAN设备显示Variable数据
                if (deviceRuntime.IsCollect == true)
                {
                    VariableRuntimes = deviceRuntime.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null);
                }
                else
                {
                    VariableRuntimes = deviceRuntime.Driver?.IdVariableRuntimes?.Where(a => a.Value != null)
    .Select(a => a.Value) ?? Enumerable.Empty<VariableRuntime>();
                }
                CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
            }

            ChannelRuntimes = Enumerable.Repeat(deviceRuntime.ChannelRuntime, 1);
            DeviceRuntimes = Enumerable.Repeat(deviceRuntime, 1);
        }
        else if (channelDeviceTreeItem.TryGetPluginName(out var pluginName))
        {
            // 插件名称 - 只选择第一个通道或设备
            var firstChannel = channels.Where(a => a.PluginName == pluginName).FirstOrDefault();
            var firstDevice = devices.Where(a => a.PluginName == pluginName).FirstOrDefault();

            if (firstChannel != null)
            {
                // 检查是否为CAN通道
                if (firstChannel.ChannelType == ChannelTypeEnum.CAN)
                {
                    CanBmsRuntimes = await GetCanBmsRuntimesForChannelAsync(firstChannel);
                    VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                }
                else
                {
                    var pluginType = GlobalData.PluginService.GetList().FirstOrDefault(a => a.FullName == pluginName)?.PluginType;
                    if (pluginType == PluginTypeEnum.Collect)
                    {
                        VariableRuntimes = firstChannel.ReadDeviceRuntimes.SelectMany(a => a.Value.ReadOnlyVariableRuntimes).Select(a => a.Value).Where(a => a != null);
                    }
                    else
                    {
                        VariableRuntimes = firstChannel.ReadDeviceRuntimes.Where(a => a.Value.Driver?.IdVariableRuntimes != null).SelectMany(a => a.Value.Driver?.IdVariableRuntimes).Select(a => a.Value);
                    }
                    CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                }

                ChannelRuntimes = Enumerable.Repeat(firstChannel, 1);
                DeviceRuntimes = firstChannel.ReadDeviceRuntimes.Select(a => a.Value);
            }
            else if (firstDevice != null)
            {
                // 检查是否为CAN设备
                if (firstDevice.IsCanDevice())
                {
                    CanBmsRuntimes = await GetCanBmsRuntimesForDeviceAsync(firstDevice);
                    VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                }
                else
                {
                    if (firstDevice.IsCollect == true)
                    {
                        VariableRuntimes = firstDevice.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null);
                    }
                    else
                    {
                        VariableRuntimes = firstDevice.Driver?.IdVariableRuntimes?.Where(a => a.Value != null).Select(a => a.Value) ?? Enumerable.Empty<VariableRuntime>();
                    }
                    CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                }

                ChannelRuntimes = Enumerable.Repeat(firstDevice.ChannelRuntime, 1);
                DeviceRuntimes = Enumerable.Repeat(firstDevice, 1);
            }
            else
            {
                ChannelRuntimes = Enumerable.Empty<ChannelRuntime>();
                DeviceRuntimes = Enumerable.Empty<DeviceRuntime>();
                VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
            }
        }
        else
        {
            // 对于插件类型的选择，只显示第一个通道或设备
            if (channelDeviceTreeItem.TryGetPluginType(out var pluginTypeEnum))
            {
                if (pluginTypeEnum != null)
                {
                    // 插件类型 - 只选择第一个通道或设备
                    var firstChannel = channels.Where(a => a.PluginType == pluginTypeEnum).FirstOrDefault();
                    var firstDevice = devices.Where(a => a.PluginType == pluginTypeEnum).FirstOrDefault();

                    if (firstChannel != null)
                    {
                        if (firstChannel.ChannelType == ChannelTypeEnum.CAN)
                        {
                            CanBmsRuntimes = await GetCanBmsRuntimesForChannelAsync(firstChannel);
                            VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        }
                        else
                        {
                            if (firstChannel.IsCollect == true)
                            {
                                VariableRuntimes = firstChannel.ReadDeviceRuntimes.SelectMany(a => a.Value.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null));
                            }
                            else
                            {
                                VariableRuntimes = firstChannel.ReadDeviceRuntimes.Where(a => a.Value?.Driver?.IdVariableRuntimes != null).SelectMany(a => a.Value?.Driver?.IdVariableRuntimes?.Where(a => a.Value != null)?.Select(a => a.Value)).Where(a => a != null);
                            }
                            CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                        }

                        ChannelRuntimes = Enumerable.Repeat(firstChannel, 1);
                        DeviceRuntimes = firstChannel.ReadDeviceRuntimes.Select(a => a.Value);
                    }
                    else if (firstDevice != null)
                    {
                        if (firstDevice.IsCanDevice())
                        {
                            CanBmsRuntimes = await GetCanBmsRuntimesForDeviceAsync(firstDevice);
                            VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        }
                        else
                        {
                            if (firstDevice.IsCollect == true)
                            {
                                VariableRuntimes = firstDevice.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null);
                            }
                            else
                            {
                                VariableRuntimes = firstDevice.Driver?.IdVariableRuntimes?.Where(a => a.Value != null).Select(a => a.Value) ?? Enumerable.Empty<VariableRuntime>();
                            }
                            CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                        }

                        ChannelRuntimes = Enumerable.Repeat(firstDevice.ChannelRuntime, 1);
                        DeviceRuntimes = Enumerable.Repeat(firstDevice, 1);
                    }
                    else
                    {
                        ChannelRuntimes = Enumerable.Empty<ChannelRuntime>();
                        DeviceRuntimes = Enumerable.Empty<DeviceRuntime>();
                        VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                    }
                }
                else
                {
                    // 显示第一个通道或设备
                    var firstChannel = channels.FirstOrDefault();
                    var firstDevice = devices.FirstOrDefault();

                    if (firstChannel != null)
                    {
                        if (firstChannel.ChannelType == ChannelTypeEnum.CAN)
                        {
                            CanBmsRuntimes = await GetCanBmsRuntimesForChannelAsync(firstChannel);
                            VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        }
                        else
                        {
                            if (firstChannel.IsCollect == true)
                            {
                                VariableRuntimes = firstChannel.ReadDeviceRuntimes.SelectMany(a => a.Value.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null));
                            }
                            else
                            {
                                VariableRuntimes = firstChannel.ReadDeviceRuntimes.Where(a => a.Value?.Driver?.IdVariableRuntimes != null).SelectMany(a => a.Value?.Driver?.IdVariableRuntimes?.Where(a => a.Value != null)?.Select(a => a.Value)).Where(a => a != null);
                            }
                            CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                        }

                        ChannelRuntimes = Enumerable.Repeat(firstChannel, 1);
                        DeviceRuntimes = firstChannel.ReadDeviceRuntimes.Select(a => a.Value);
                    }
                    else if (firstDevice != null)
                    {
                        if (firstDevice.IsCanDevice())
                        {
                            CanBmsRuntimes = await GetCanBmsRuntimesForDeviceAsync(firstDevice);
                            VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        }
                        else
                        {
                            if (firstDevice.IsCollect == true)
                            {
                                VariableRuntimes = firstDevice.ReadOnlyVariableRuntimes.Select(a => a.Value).Where(a => a != null);
                            }
                            else
                            {
                                VariableRuntimes = firstDevice.Driver?.IdVariableRuntimes?.Where(a => a.Value != null).Select(a => a.Value) ?? Enumerable.Empty<VariableRuntime>();
                            }
                            CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                        }

                        ChannelRuntimes = Enumerable.Repeat(firstDevice.ChannelRuntime, 1);
                        DeviceRuntimes = Enumerable.Repeat(firstDevice, 1);
                    }
                    else
                    {
                        ChannelRuntimes = Enumerable.Empty<ChannelRuntime>();
                        DeviceRuntimes = Enumerable.Empty<DeviceRuntime>();
                        VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                        CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                    }
                }
            }
            else
            {
                VariableRuntimes = Enumerable.Empty<VariableRuntime>();
                CanBmsRuntimes = Enumerable.Empty<CanBmsRuntime>();
                ChannelRuntimes = Enumerable.Empty<ChannelRuntime>();
                DeviceRuntimes = Enumerable.Empty<DeviceRuntime>();
            }
        }
        await InvokeAsync(StateHasChanged);
    }

    #endregion 查询
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await TreeChangedAsync(SelectModel);
        await base.OnAfterRenderAsync(firstRender);
    }
    public IEnumerable<VariableRuntime> VariableRuntimes { get; set; } = Enumerable.Empty<VariableRuntime>();
    public IEnumerable<CanBmsRuntime> CanBmsRuntimes { get; set; } = Enumerable.Empty<CanBmsRuntime>();
    public IEnumerable<CanSignalRuntime> CanSignalRuntimes { get; set; } = Enumerable.Empty<CanSignalRuntime>();
    public IEnumerable<ChannelRuntime> ChannelRuntimes { get; set; } = Enumerable.Empty<ChannelRuntime>();
    public IEnumerable<DeviceRuntime> DeviceRuntimes { get; set; } = Enumerable.Empty<DeviceRuntime>();


    private long ShowChannelRuntime { get; set; }
    private long ShowDeviceRuntime { get; set; }
    public ShowTypeEnum? ShowType { get; set; }
    private bool AutoRestartThread { get; set; } = true;

    /// <summary>
    /// 获取通道的CAN BMS运行时数据
    /// </summary>
    /// <param name="channelRuntime">通道运行时</param>
    /// <returns>CAN BMS运行时列表</returns>
    private async Task<IEnumerable<CanBmsRuntime>> GetCanBmsRuntimesForChannelAsync(ChannelRuntime channelRuntime)
    {
        var canBmsRuntimes = new List<CanBmsRuntime>();

        foreach (var deviceRuntime in channelRuntime.ReadDeviceRuntimes.Select(a => a.Value))
        {
            if (deviceRuntime.IsCanDevice() && deviceRuntime.Driver != null)
            {
                // 通过反射调用CAN插件的GetCanDeviceRuntime方法
                var method = deviceRuntime.Driver.GetType().GetMethod("GetCanDeviceRuntime");
                if (method != null)
                {
                    var canDeviceRuntime = method.Invoke(deviceRuntime.Driver, null) as CanDeviceRuntime;
                    if (canDeviceRuntime != null)
                    {
                        canBmsRuntimes.AddRange(canDeviceRuntime.BmsRuntimes);
                    }
                }
            }
        }

        return canBmsRuntimes;
    }

    /// <summary>
    /// 获取设备的CAN BMS运行时数据
    /// </summary>
    /// <param name="deviceRuntime">设备运行时</param>
    /// <returns>CAN BMS运行时列表</returns>
    private async Task<IEnumerable<CanBmsRuntime>> GetCanBmsRuntimesForDeviceAsync(DeviceRuntime deviceRuntime)
    {
        if (deviceRuntime.IsCanDevice() && deviceRuntime.Driver != null)
        {
            // 通过反射调用CAN插件的GetCanDeviceRuntime方法
            var method = deviceRuntime.Driver.GetType().GetMethod("GetCanDeviceRuntime");
            if (method != null)
            {
                var canDeviceRuntime = method.Invoke(deviceRuntime.Driver, null) as CanDeviceRuntime;
                if (canDeviceRuntime != null)
                {
                    return canDeviceRuntime.BmsRuntimes;
                }
            }
        }

        return Enumerable.Empty<CanBmsRuntime>();
    }

}

/// <summary>
/// DeviceRuntime扩展方法
/// </summary>
public static class DeviceRuntimeExtensions
{
    /// <summary>
    /// 判断设备是否为CAN设备
    /// </summary>
    /// <param name="deviceRuntime">设备运行时</param>
    /// <returns>是否为CAN设备</returns>
    public static bool IsCanDevice(this DeviceRuntime deviceRuntime)
    {
        return deviceRuntime?.ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN;
    }
}

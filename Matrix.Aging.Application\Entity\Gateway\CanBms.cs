using BootstrapBlazor.Components;
using Matrix.Foundation;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN BMS配置表（协议文件管理）
/// </summary>
[SugarTable("can_bms", TableDescription = "CAN BMS配置表")]
[Tenant(SqlSugarConst.DB_Custom)]
[SugarIndex("unique_bms_name", nameof(CanBms.BmsName), OrderByType.Asc, true)]
public class CanBms : BaseDataEntity, IValidatableObject
{
    /// <summary>
    /// BMS名称
    /// </summary>
    [SugarColumn(ColumnDescription = "BMS名称", Length = 100, IsNullable = false)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 1)]
    [Required]
    public virtual string BmsName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? Description { get; set; }

    /// <summary>
    /// 使能
    /// </summary>
    [SugarColumn(ColumnDescription = "使能")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool Enable { get; set; } = true;

    /// <summary>
    /// 发送使能（协议文件级默认发送配置）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送使能")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool SendEnable { get; set; } = false;

    /// <summary>
    /// 默认发送消息ID（协议文件级默认发送配置）
    /// </summary>
    [SugarColumn(ColumnDescription = "默认发送消息ID", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual uint? SendMessageId { get; set; }

    /// <summary>
    /// 验证方法
    /// </summary>
    /// <param name="validationContext">验证上下文</param>
    /// <returns>验证结果</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // CanBms现在只是协议文件容器，验证逻辑已移至CanMessage中
        // 这里只需要验证基本的必填字段（通过Required特性已处理）

        return results;
    }
}

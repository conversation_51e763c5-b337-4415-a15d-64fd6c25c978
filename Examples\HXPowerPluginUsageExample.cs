using Matrix.Foundation;
using Matrix.PowerPlugin.HXPower;
using Matrix.Aging.Application;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;

namespace Examples;

/// <summary>
/// HXPower插件正确使用示例
/// 展示如何在不侵入主项目的情况下，通过插件处理HXPower协议
/// </summary>
public class HXPowerPluginUsageExample
{
    /// <summary>
    /// 演示HXPower插件的正确使用方法
    /// </summary>
    public static async Task RunExampleAsync()
    {
        Console.WriteLine("=== HXPower插件使用示例 ===");
        Console.WriteLine("展示如何通过插件接口发送设备模式、电流值、功率值等参数");
        Console.WriteLine();

        // 1. 创建HXPower插件实例
        var hxPowerDriver = new HXPowerCollectDriver();
        
        // 2. 模拟初始化（实际使用时需要真实的CAN通道）
        // var canChannel = new CanChannel();
        // await hxPowerDriver.InitChannelAsync(canChannel, CancellationToken.None);
        Console.WriteLine("✓ HXPower插件初始化完成");
        
        // 3. 演示不同的工步命令
        await DemonstrateStepCommands(hxPowerDriver);
        
        // 4. 演示参数化控制
        await DemonstrateParameterizedControl(hxPowerDriver);
        
        // 5. 演示批量控制
        await DemonstrateBatchControl(hxPowerDriver);
        
        Console.WriteLine("\n=== 插件使用示例完成 ===");
    }

    /// <summary>
    /// 演示基本工步命令
    /// </summary>
    private static async Task DemonstrateStepCommands(HXPowerCollectDriver driver)
    {
        Console.WriteLine("\n--- 演示基本工步命令 ---");
        
        // 获取设备实例
        var device = driver.FoundationDevice;
        if (device == null)
        {
            Console.WriteLine("❌ 设备未初始化");
            return;
        }
        
        // 1. 发送ST命令（静置模式）
        Console.WriteLine("\n1. 发送ST命令（静置模式）");
        var stCommand = JToken.FromObject(new
        {
            Mode = "ST",
            Current = 0.0,
            Voltage = 0.0,
            Power = 0.0
        });
        
        var result = await device.WriteAsync("1.1", stCommand, DataTypeEnum.String);
        Console.WriteLine($"   结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
        
        // 2. 发送CC命令（恒流模式）
        Console.WriteLine("\n2. 发送CC命令（恒流充电10A）");
        var ccCommand = JToken.FromObject(new
        {
            Mode = "CC",
            Current = 10.0,
            Voltage = 0.0,
            Power = 0.0
        });
        
        result = await device.WriteAsync("1.1", ccCommand, DataTypeEnum.String);
        Console.WriteLine($"   结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
        
        // 3. 发送CV命令（恒压模式）
        Console.WriteLine("\n3. 发送CV命令（恒压充电58.4V）");
        var cvCommand = JToken.FromObject(new
        {
            Mode = "CV",
            Current = 0.0,
            Voltage = 58.4,
            Power = 0.0
        });
        
        result = await device.WriteAsync("1.1", cvCommand, DataTypeEnum.String);
        Console.WriteLine($"   结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
        
        // 4. 发送CP命令（恒功率模式）
        Console.WriteLine("\n4. 发送CP命令（恒功率充电500W）");
        var cpCommand = JToken.FromObject(new
        {
            Mode = "CP",
            Current = 0.0,
            Voltage = 0.0,
            Power = 500.0
        });
        
        result = await device.WriteAsync("1.1", cpCommand, DataTypeEnum.String);
        Console.WriteLine($"   结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
    }

    /// <summary>
    /// 演示参数化控制
    /// </summary>
    private static async Task DemonstrateParameterizedControl(HXPowerCollectDriver driver)
    {
        Console.WriteLine("\n--- 演示参数化控制 ---");
        
        var device = driver.FoundationDevice;
        if (device == null) return;
        
        // 支持数字模式和字符串模式
        var parameters = new[]
        {
            new { Mode = 1, Current = 5.0, Voltage = 50.0, Power = 0.0, Description = "数字模式1（恒流）" },
            new { Mode = 2, Current = 0.0, Voltage = 55.0, Power = 0.0, Description = "数字模式2（恒压）" },
            new { Mode = "CC", Current = 8.0, Voltage = 0.0, Power = 0.0, Description = "字符串模式CC" },
            new { Mode = "CV", Current = 0.0, Voltage = 58.4, Power = 0.0, Description = "字符串模式CV" },
            new { Mode = "CP", Current = 0.0, Voltage = 0.0, Power = 300.0, Description = "字符串模式CP" }
        };
        
        foreach (var param in parameters)
        {
            Console.WriteLine($"\n发送参数: {param.Description}");
            Console.WriteLine($"   模式={param.Mode}, 电流={param.Current}A, 电压={param.Voltage}V, 功率={param.Power}W");
            
            var command = JToken.FromObject(param);
            var result = await device.WriteAsync("2.1", command, DataTypeEnum.String);
            Console.WriteLine($"   结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 演示批量控制
    /// </summary>
    private static async Task DemonstrateBatchControl(HXPowerCollectDriver driver)
    {
        Console.WriteLine("\n--- 演示批量控制 ---");
        
        var device = driver.FoundationDevice;
        if (device == null) return;
        
        // 控制多个模块的多个通道
        var controlTasks = new List<Task<OperResult>>();
        
        // 模块1-3，每个模块2个通道
        for (int module = 1; module <= 3; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                var address = $"{module}.{channel}";
                var command = JToken.FromObject(new
                {
                    Mode = "CC",
                    Current = 5.0 + module, // 每个模块不同的电流值
                    Voltage = 0.0,
                    Power = 0.0
                });
                
                Console.WriteLine($"启动控制: 模块{module}通道{channel}, 电流={5.0 + module}A");
                controlTasks.Add(device.WriteAsync(address, command, DataTypeEnum.String));
            }
        }
        
        // 等待所有控制命令完成
        var results = await Task.WhenAll(controlTasks);
        
        Console.WriteLine("\n批量控制结果:");
        int index = 0;
        for (int module = 1; module <= 3; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                var result = results[index++];
                Console.WriteLine($"   模块{module}通道{channel}: {(result.IsSuccess ? "成功" : "失败")}");
            }
        }
    }

    /// <summary>
    /// 展示插件架构说明
    /// </summary>
    public static void ExplainPluginArchitecture()
    {
        Console.WriteLine("\n=== HXPower插件架构说明 ===");
        Console.WriteLine();
        Console.WriteLine("🏗️ 插件架构优势:");
        Console.WriteLine("  ✅ 不侵入主项目Foundation和Aging.Application");
        Console.WriteLine("  ✅ 标准化接口，支持多种电源插件");
        Console.WriteLine("  ✅ 插件内部处理协议转换");
        Console.WriteLine("  ✅ 支持扩展，可添加HYPower等其他电源插件");
        Console.WriteLine();
        Console.WriteLine("📋 接口规范:");
        Console.WriteLine("  - 地址格式: \"ModuleNumber.ChannelNumber\" (如 \"1.1\")");
        Console.WriteLine("  - 参数格式: JSON对象 {Mode, Current, Voltage, Power}");
        Console.WriteLine("  - 模式支持: 数字(1-7)或字符串(ST/CC/CV/CP/CR)");
        Console.WriteLine("  - 返回类型: OperResult (成功/失败状态)");
        Console.WriteLine();
        Console.WriteLine("🔄 工作流程:");
        Console.WriteLine("  1. 主项目调用 device.WriteAsync(address, parameters)");
        Console.WriteLine("  2. 插件接收参数并解析地址和模式");
        Console.WriteLine("  3. 插件内部生成HXPower协议数据");
        Console.WriteLine("  4. 插件通过CAN通道发送报文");
        Console.WriteLine("  5. 返回操作结果给主项目");
        Console.WriteLine();
        Console.WriteLine("⚡ 支持的工作模式:");
        Console.WriteLine("  - ST/1: 静置模式");
        Console.WriteLine("  - CC/2: 恒流充电");
        Console.WriteLine("  - CV/3: 恒压充电");
        Console.WriteLine("  - CP/6: 恒功率充电");
        Console.WriteLine("  - CR/4: 恒阻充电");
        Console.WriteLine();
        Console.WriteLine("📊 参数示例:");
        Console.WriteLine("  静置: {Mode: \"ST\", Current: 0, Voltage: 0, Power: 0}");
        Console.WriteLine("  恒流: {Mode: \"CC\", Current: 10.0, Voltage: 0, Power: 0}");
        Console.WriteLine("  恒压: {Mode: \"CV\", Current: 0, Voltage: 58.4, Power: 0}");
        Console.WriteLine("  恒功率: {Mode: \"CP\", Current: 0, Voltage: 0, Power: 500.0}");
        Console.WriteLine();
        Console.WriteLine("🚀 扩展性:");
        Console.WriteLine("  - 可以添加HYPower插件，使用相同的接口");
        Console.WriteLine("  - 可以添加其他品牌电源插件");
        Console.WriteLine("  - 主项目无需修改，只需加载不同插件");
    }

    /// <summary>
    /// 演示老化测试场景
    /// </summary>
    public static async Task DemonstrateAgingScenario()
    {
        Console.WriteLine("\n=== 老化测试场景演示 ===");
        
        var driver = new HXPowerCollectDriver();
        var device = driver.FoundationDevice;
        
        if (device == null)
        {
            Console.WriteLine("❌ 设备未初始化");
            return;
        }
        
        // 模拟老化测试流程
        var agingSteps = new[]
        {
            new { Name = "静置", Mode = "ST", Current = 0.0, Voltage = 0.0, Power = 0.0, Duration = 5 },
            new { Name = "恒流充电", Mode = "CC", Current = 10.0, Voltage = 0.0, Power = 0.0, Duration = 30 },
            new { Name = "恒压充电", Mode = "CV", Current = 0.0, Voltage = 58.4, Power = 0.0, Duration = 60 },
            new { Name = "静置", Mode = "ST", Current = 0.0, Voltage = 0.0, Power = 0.0, Duration = 10 },
            new { Name = "恒流放电", Mode = "CC", Current = -10.0, Voltage = 0.0, Power = 0.0, Duration = 45 },
            new { Name = "结束静置", Mode = "ST", Current = 0.0, Voltage = 0.0, Power = 0.0, Duration = 5 }
        };
        
        Console.WriteLine("开始老化测试流程...");
        
        foreach (var step in agingSteps)
        {
            Console.WriteLine($"\n执行工步: {step.Name}");
            Console.WriteLine($"   参数: 模式={step.Mode}, 电流={step.Current}A, 电压={step.Voltage}V, 功率={step.Power}W");
            Console.WriteLine($"   持续时间: {step.Duration}秒");
            
            var command = JToken.FromObject(new
            {
                Mode = step.Mode,
                Current = step.Current,
                Voltage = step.Voltage,
                Power = step.Power
            });
            
            var result = await device.WriteAsync("1.1", command, DataTypeEnum.String);
            Console.WriteLine($"   发送结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 模拟工步运行时间
            await Task.Delay(1000); // 实际应该是 step.Duration * 1000
            Console.WriteLine($"   工步 {step.Name} 完成");
        }
        
        Console.WriteLine("\n✅ 老化测试流程完成");
    }
} 
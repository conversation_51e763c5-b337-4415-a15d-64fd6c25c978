namespace Matrix.Foundation.HXPower;

/// <summary>
/// HXPower数据处理器
/// </summary>
public static class HXPowerDataProcessor
{
    /// <summary>
    /// 设备工作模式枚举
    /// </summary>
    public enum HXPowerWorkMode : byte
    {
        /// <summary>
        /// 待机
        /// </summary>
        Standby = 0x00,
        /// <summary>
        /// 恒压（正值充电，负值放电）
        /// </summary>
        ConstantVoltage = 0x01,
        /// <summary>
        /// 恒流（正值充电，负值放电）
        /// </summary>
        ConstantCurrent = 0x02,
        /// <summary>
        /// 恒功率（正值充电，负值放电）
        /// </summary>
        ConstantPower = 0x03,
        /// <summary>
        /// 恒阻（正值充电，负值放电）
        /// </summary>
        ConstantResistance = 0x04,
        /// <summary>
        /// 搁置
        /// </summary>
        Rest = 0x05,
        /// <summary>
        /// 恒流恒压（恒压值，正值充电，负值放电）
        /// </summary>
        ConstantCurrentVoltage = 0x06
    }

    /// <summary>
    /// HXPower设备状态数据
    /// </summary>
    public class HXPowerDeviceStatus
    {
        /// <summary>
        /// 模块编号
        /// </summary>
        public int ModuleNumber { get; set; }

        /// <summary>
        /// 通道编号
        /// </summary>
        public int ChannelNumber { get; set; }

        /// <summary>
        /// 设备状态
        /// </summary>
        public HXPowerWorkMode DeviceStatus { get; set; }

        /// <summary>
        /// 电压值（V）
        /// </summary>
        public double Voltage { get; set; }

        /// <summary>
        /// 电流值（A）
        /// </summary>
        public double Current { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// HXPower控制命令
    /// </summary>
    public class HXPowerControlCommand
    {
        /// <summary>
        /// 模块编号（1-10）
        /// </summary>
        public int ModuleNumber { get; set; }

        /// <summary>
        /// 通道编号（1-2）
        /// </summary>
        public int ChannelNumber { get; set; }

        /// <summary>
        /// 工作模式
        /// </summary>
        public HXPowerWorkMode WorkMode { get; set; }

        /// <summary>
        /// 控制值（电压/电流/功率/阻抗）
        /// </summary>
        public double ControlValue { get; set; }

        /// <summary>
        /// 生成控制命令的CAN ID
        /// </summary>
        public uint GetCanId()
        {
            return (uint)(0x04904102 + (ModuleNumber - 1) * 2 + (ChannelNumber - 1));
        }
    }

    /// <summary>
    /// 解析数据报文
    /// </summary>
    public static HXPowerDeviceStatus ParseDataFrame(uint canId, byte[] data)
    {
        if (data.Length < 8)
            throw new ArgumentException("数据长度不足");

        // 根据CAN ID计算模块和通道号
        var channelOffset = canId - 0x0810FF41;
        var moduleNumber = (int)(channelOffset / 2) + 1;
        var channelNumber = (int)(channelOffset % 2) + 1;

        var status = new HXPowerDeviceStatus
        {
            ModuleNumber = moduleNumber,
            ChannelNumber = channelNumber,
            DeviceStatus = (HXPowerWorkMode)data[0],
            LastUpdateTime = DateTime.Now
        };

        // 解析电压（第2-4字节，分辨率0.00001V）
        var voltageBytes = new byte[4];
        Array.Copy(data, 1, voltageBytes, 0, 3);
        var voltageRaw = BitConverter.ToUInt32(voltageBytes, 0);
        status.Voltage = voltageRaw * 0.00001;

        // 解析电流（第5-8字节，分辨率0.00001A，有符号）
        var currentBytes = new byte[4];
        Array.Copy(data, 4, currentBytes, 0, 4);
        var currentRaw = BitConverter.ToInt32(currentBytes, 0);
        status.Current = currentRaw * 0.00001;

        return status;
    }

    /// <summary>
    /// 生成控制命令数据包
    /// </summary>
    public static byte[] GenerateControlCommandData(HXPowerControlCommand command)
    {
        var data = new byte[8];
        data[0] = 0x01; // 固定值
        data[1] = 0x00; // 固定值
        data[2] = (byte)command.WorkMode; // 工作模式
        data[3] = 0x00; // 固定值

        // 控制值转换为4字节（分辨率0.00001）
        var controlValueInt = (int)(command.ControlValue / 0.00001);
        var controlValueBytes = BitConverter.GetBytes(controlValueInt);
        
        // Intel字节序处理
        if (BitConverter.IsLittleEndian)
        {
            Array.Copy(controlValueBytes, 0, data, 4, 4);
        }
        else
        {
            data[4] = controlValueBytes[3];
            data[5] = controlValueBytes[2];
            data[6] = controlValueBytes[1];
            data[7] = controlValueBytes[0];
        }

        return data;
    }

    /// <summary>
    /// 解析心跳报文
    /// </summary>
    public static byte ParseHeartbeatFrame(byte[] data)
    {
        if (data.Length < 1)
            throw new ArgumentException("心跳数据长度不足");

        return data[0]; // 生命帧
    }

    /// <summary>
    /// 解析故障报文
    /// </summary>
    public static (int ModuleNumber, int ChannelNumber, ulong FaultCode) ParseFaultFrame(uint canId, byte[] data)
    {
        if (data.Length < 8)
            throw new ArgumentException("故障数据长度不足");

        // 根据CAN ID计算模块和通道号
        var channelOffset = canId - 0x1040ff41;
        var moduleNumber = (int)(channelOffset / 2) + 1;
        var channelNumber = (int)(channelOffset % 2) + 1;

        var faultCode = BitConverter.ToUInt64(data, 0);

        return (moduleNumber, channelNumber, faultCode);
    }

    /// <summary>
    /// 验证CAN ID是否为HXPower数据报文
    /// </summary>
    public static bool IsDataFrame(uint canId)
    {
        return canId >= 0x0810FF41 && canId <= 0x0810FF54;
    }

    /// <summary>
    /// 验证CAN ID是否为HXPower心跳报文
    /// </summary>
    public static bool IsHeartbeatFrame(uint canId)
    {
        return canId == 0x1020FF02;
    }

    /// <summary>
    /// 验证CAN ID是否为HXPower故障报文
    /// </summary>
    public static bool IsFaultFrame(uint canId)
    {
        return canId >= 0x1040ff41 && canId <= 0x1040ff54;
    }

    /// <summary>
    /// 验证CAN ID是否为HXPower控制命令
    /// </summary>
    public static bool IsControlFrame(uint canId)
    {
        return canId >= 0x04904102 && canId <= 0x04904115;
    }

    /// <summary>
    /// 从CAN ID计算模块和通道号
    /// </summary>
    public static (int ModuleNumber, int ChannelNumber) GetModuleChannelFromDataCanId(uint canId)
    {
        var channelOffset = canId - 0x0810FF41;
        var moduleNumber = (int)(channelOffset / 2) + 1;
        var channelNumber = (int)(channelOffset % 2) + 1;
        return (moduleNumber, channelNumber);
    }

    /// <summary>
    /// 从模块和通道号计算数据报文CAN ID
    /// </summary>
    public static uint GetDataCanIdFromModuleChannel(int moduleNumber, int channelNumber)
    {
        return (uint)(0x0810FF41 + (moduleNumber - 1) * 2 + (channelNumber - 1));
    }

    /// <summary>
    /// 从模块和通道号计算控制命令CAN ID
    /// </summary>
    public static uint GetControlCanId(int moduleNumber, int channelNumber)
    {
        return (uint)(0x04904102 + (moduleNumber - 1) * 2 + (channelNumber - 1));
    }

    /// <summary>
    /// 生成控制命令数据包（重载方法）
    /// </summary>
    /// <param name="moduleNumber">模块号</param>
    /// <param name="channelNumber">通道号</param>
    /// <param name="workMode">工作模式</param>
    /// <param name="current">电流值</param>
    /// <param name="voltage">电压值</param>
    /// <param name="power">功率值</param>
    /// <returns>控制命令数据包</returns>
    public static byte[] GenerateControlCommandData(int moduleNumber, int channelNumber, 
        HXPowerWorkMode workMode, double current, double voltage, double power)
    {
        var command = new HXPowerControlCommand
        {
            ModuleNumber = moduleNumber,
            ChannelNumber = channelNumber,
            WorkMode = workMode,
            ControlValue = workMode switch
            {
                HXPowerWorkMode.ConstantCurrent => current,
                HXPowerWorkMode.ConstantVoltage => voltage,
                HXPowerWorkMode.ConstantPower => power,
                HXPowerWorkMode.ConstantResistance => current, // 恒阻模式使用电流值
                _ => 0.0
            }
        };

        return GenerateControlCommandData(command);
    }
} 
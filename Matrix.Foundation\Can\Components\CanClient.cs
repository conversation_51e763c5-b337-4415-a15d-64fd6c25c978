using System;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;

namespace Matrix.Foundation;

/// <summary>
/// 通用CAN客户端
/// 根据配置自动选择合适的CAN实现
/// </summary>
public class CanClient : CanClientBase, ICanClient
{
    #region 字段

    private ICanClient m_actualClient;

    #endregion 字段

    #region 事件

    /// <inheritdoc/>
    public ConnectedEventHandler<ICanClient> Connected { get; set; }

    /// <inheritdoc/>
    public ConnectingEventHandler<ICanClient> Connecting { get; set; }

    /// <inheritdoc/>
    public ClosedEventHandler<ICanClient> Closed { get; set; }

    /// <inheritdoc/>
    public ClosingEventHandler<ICanClient> Closing { get; set; }

    /// <inheritdoc/>
    public CanReceivedEventHandler<ICanClient> Received { get; set; }

    #endregion 事件

    #region 属性
    /// <summary>
    /// 实际使用的CAN客户端
    /// </summary>
    public ICanClient ActualClient => this.m_actualClient;

    #endregion 属性

    #region 连接操作

    /// <inheritdoc/>
    protected override async Task ProtectedConnectAsync(int millisecondsTimeout, CancellationToken token)
    {
        try
        {
            // 根据配置创建具体的CAN客户端
            this.m_actualClient = this.CreateActualClient();

            // 设置配置
            await this.m_actualClient.SetupAsync(this.Config).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            // 绑定事件
            this.BindEvents();

            // 连接
            await this.m_actualClient.ConnectAsync(millisecondsTimeout, token).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            this.Logger?.Info($"CAN客户端连接成功，使用实现: {this.m_actualClient.GetType().Name}");
        }
        catch (Exception ex)
        {
            throw new Exception($"CAN客户端连接失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 根据配置创建具体的CAN客户端
    /// </summary>
    /// <returns></returns>
    private ICanClient CreateActualClient()
    {
        // 检查配置是否有效
        if (this.m_channelOptions == null || string.IsNullOrEmpty(this.CanSet))
        {
            throw new ArgumentNullException("CAN配置不能为空，请设置ChannelOptions的CAN配置");
        }

        ICanClient client = this.CanType switch
        {
            CanBusType.SocketCan => new SocketCanClient(),
            CanBusType.EthernetCan => new EthernetCanClient(),
            CanBusType.ZlgCan => new ZlgCanClient(),
            CanBusType.Other => throw new NotSupportedException("其他CAN类型暂未实现"),
            _ => throw new ArgumentException($"不支持的CAN总线类型: {this.CanType}")
        };

        // 将配置传递给具体的客户端
        if (client is CanClientBase clientBase)
        {
            clientBase.SetChannelOptions(this.m_channelOptions);
        }

        return client;
    }

    /// <summary>
    /// 绑定事件
    /// </summary>
    private void BindEvents()
    {
        if (this.m_actualClient != null)
        {
            this.m_actualClient.Connected += this.OnActualClientConnected;
            this.m_actualClient.Connecting += this.OnActualClientConnecting;
            this.m_actualClient.Closed += this.OnActualClientClosed;
            this.m_actualClient.Closing += this.OnActualClientClosing;
            this.m_actualClient.Received += this.OnActualClientReceived;
        }
    }

    /// <summary>
    /// 解绑事件
    /// </summary>
    private void UnbindEvents()
    {
        if (this.m_actualClient != null)
        {
            this.m_actualClient.Connected -= this.OnActualClientConnected;
            this.m_actualClient.Connecting -= this.OnActualClientConnecting;
            this.m_actualClient.Closed -= this.OnActualClientClosed;
            this.m_actualClient.Closing -= this.OnActualClientClosing;
            this.m_actualClient.Received -= this.OnActualClientReceived;
        }
    }

    #endregion 连接操作

    #region 断开操作

    /// <inheritdoc/>
    protected override async Task ProtectedCloseAsync(string msg)
    {
        try
        {
            if (this.m_actualClient != null)
            {
                this.UnbindEvents();
                await this.m_actualClient.CloseAsync(msg).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
                this.m_actualClient.SafeDispose();
                this.m_actualClient = null;
            }

            this.Logger?.Info($"CAN客户端连接已关闭: {msg}");
        }
        catch (Exception ex)
        {
            this.Logger?.Error(this, $"关闭CAN客户端连接时发生错误: {ex.Message}");
        }
    }

    #endregion 断开操作

    #region 发送操作

    /// <inheritdoc/>
    protected override async Task ProtectedSendAsync(CanFrame frame)
    {
        this.ThrowIfNotConnected();

        if (this.m_actualClient == null)
        {
            throw new InvalidOperationException("实际CAN客户端未初始化");
        }

        await this.m_actualClient.SendAsync(frame).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    #endregion 发送操作

    #region 实际客户端事件处理

    /// <summary>
    /// 实际客户端连接成功事件
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnActualClientConnected(ICanClient client, ConnectedEventArgs e)
    {
        if (this.Connected != null)
        {
            await this.Connected.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await this.OnCanConnected(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <summary>
    /// 实际客户端准备连接事件
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnActualClientConnecting(ICanClient client, ConnectingEventArgs e)
    {
        if (this.Connecting != null)
        {
            await this.Connecting.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await this.OnCanConnecting(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <summary>
    /// 实际客户端连接关闭事件
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnActualClientClosed(ICanClient client, ClosedEventArgs e)
    {
        if (this.Closed != null)
        {
            await this.Closed.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await this.OnCanClosed(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <summary>
    /// 实际客户端即将关闭事件
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnActualClientClosing(ICanClient client, ClosingEventArgs e)
    {
        if (this.Closing != null)
        {
            await this.Closing.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await this.OnCanClosing(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <summary>
    /// 实际客户端接收数据事件
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnActualClientReceived(ICanClient client, CanReceivedEventArgs e)
    {
        if (this.Received != null)
        {
            await this.Received.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await this.OnCanReceived(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    #endregion 实际客户端事件处理

    #region 重写属性

    /// <inheritdoc/>
    public override bool Online => this.m_actualClient?.Online ?? false;

    #endregion 重写属性

    #region IDisposable

    /// <inheritdoc/>
    //protected override async ValueTask DisposeCore()
    //{
    //    if (this.m_actualClient != null)
    //    {
    //        this.UnbindEvents();
    //        this.m_actualClient.SafeDispose();
    //        this.m_actualClient = null;
    //    }
    //    await base.DisposeCore().ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    //}
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            if (this.m_actualClient != null)
            {
                this.UnbindEvents();
                this.m_actualClient.SafeDispose();
                this.m_actualClient = null;
            }
        }
        base.Dispose(disposing);
    }

    #endregion IDisposable
}

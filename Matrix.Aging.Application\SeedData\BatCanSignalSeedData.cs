namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanSignal种子数据
/// </summary>
public class BatCanSignalSeedData : ISqlSugarEntitySeedData<CanSignal>
{
    /// <inheritdoc/>
    public IEnumerable<CanSignal> SeedData()
    {
        var signals = new List<CanSignal>();

        // 0x189022F4 - 基本信息信号
        // 总电压 (Byte 0-1, 16bit, 0.1V/bit)
        signals.Add(new CanSignal
        {
            Id = 120001,
            CanMessageId = 110001, // Bat_Basic_Info消息ID
            SignalName = "Total_Voltage",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 0,
            BitLength = 16,
            Resolution = 0.1,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 6553.5,
            MinValue = 0,
            Unit = "V",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "56V电池包总电压",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 总电流 (Byte 4-5, 16bit, 0.1A/bit, offset -30000)
        signals.Add(new CanSignal
        {
            Id = 120002,
            CanMessageId = 110001, // Bat_Basic_Info消息ID
            SignalName = "Total_Current",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 32,
            BitLength = 16,
            Resolution = 0.1,
            Offset = -30000.0,
            IsSigned = false,
            MaxValue = 6553.5,
            MinValue = 0,
            Unit = "A",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "56V电池包总电流",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // SOC (Byte 6-7, 16bit, 0.1%/bit)
        signals.Add(new CanSignal
        {
            Id = 120003,
            CanMessageId = 110001, // Bat_Basic_Info消息ID
            SignalName = "SOC",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 48,
            BitLength = 16,
            Resolution = 0.1,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 6553.5,
            MinValue = 0,
            Unit = "%",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "56V电池包SOC",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 0x189122F4 - 单体电压信息信号
        // 最高单体电压值 (Byte 0-1, 16bit, 1mV/bit)
        signals.Add(new CanSignal
        {
            Id = 120004,
            CanMessageId = 110002, // Bat_Cell_Voltage_Info消息ID
            SignalName = "Max_Cell_Voltage",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 0,
            BitLength = 16,
            Resolution = 0.001,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 65.535,
            MinValue = 0,
            Unit = "V",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最高单体电压值",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 最高单体电压cell号 (Byte 2, 8bit, 1/bit)
        signals.Add(new CanSignal
        {
            Id = 120005,
            CanMessageId = 110002, // Bat_Cell_Voltage_Info消息ID
            SignalName = "Max_Cell_Voltage_Number",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 16,
            BitLength = 8,
            Resolution = 1.0,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最高单体电压cell号",
            Enable = true,
            DataType = DataTypeEnum.Byte
        });

        // 最低单体电压值 (Byte 3-4, 16bit, 1mV/bit)
        signals.Add(new CanSignal
        {
            Id = 120006,
            CanMessageId = 110002, // Bat_Cell_Voltage_Info消息ID
            SignalName = "Min_Cell_Voltage",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 24,
            BitLength = 16,
            Resolution = 0.001,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 65.535,
            MinValue = 0,
            Unit = "V",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最低单体电压值",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 最低单体电压cell号 (Byte 5, 8bit, 1/bit)
        signals.Add(new CanSignal
        {
            Id = 120007,
            CanMessageId = 110002, // Bat_Cell_Voltage_Info消息ID
            SignalName = "Min_Cell_Voltage_Number",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 40,
            BitLength = 8,
            Resolution = 1.0,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最低单体电压cell号",
            Enable = true,
            DataType = DataTypeEnum.Byte
        });

        // 0x189222F4 - 单体温度信息信号
        // 最高单体温度值 (Byte 0, 8bit, 1℃/bit, offset -40)
        signals.Add(new CanSignal
        {
            Id = 120008,
            CanMessageId = 110003, // Bat_Cell_Temperature_Info消息ID
            SignalName = "Max_Cell_Temperature",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 0,
            BitLength = 8,
            Resolution = 1.0,
            Offset = -40.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "℃",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最高单体温度值",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 最高单体温度cell号 (Byte 1, 8bit, 1/bit)
        signals.Add(new CanSignal
        {
            Id = 120009,
            CanMessageId = 110003, // Bat_Cell_Temperature_Info消息ID
            SignalName = "Max_Cell_Temperature_Number",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 8,
            BitLength = 8,
            Resolution = 1.0,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最高单体温度cell号",
            Enable = true,
            DataType = DataTypeEnum.Byte
        });

        // 最低单体温度值 (Byte 2, 8bit, 1℃/bit, offset -40)
        signals.Add(new CanSignal
        {
            Id = 120010,
            CanMessageId = 110003, // Bat_Cell_Temperature_Info消息ID
            SignalName = "Min_Cell_Temperature",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 16,
            BitLength = 8,
            Resolution = 1.0,
            Offset = -40.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "℃",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最低单体温度值",
            Enable = true,
            DataType = DataTypeEnum.Double
        });

        // 最低单体温度cell号 (Byte 3, 8bit, 1/bit)
        signals.Add(new CanSignal
        {
            Id = 120011,
            CanMessageId = 110003, // Bat_Cell_Temperature_Info消息ID
            SignalName = "Min_Cell_Temperature_Number",
            ByteOrder = CanByteOrderEnum.Intel,
            StartBit = 24,
            BitLength = 8,
            Resolution = 1.0,
            Offset = 0.0,
            IsSigned = false,
            MaxValue = 255,
            MinValue = 0,
            Unit = "",
            InitialValue = 0,
            MultiPacketConfig = null,
            IsSave = true,
            IsDisplay = true,
            Description = "最低单体温度cell号",
            Enable = true,
            DataType = DataTypeEnum.Byte
        });

        return signals;
    }
} 
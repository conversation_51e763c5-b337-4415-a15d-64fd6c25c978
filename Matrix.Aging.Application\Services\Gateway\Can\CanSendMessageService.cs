using SqlSugar;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN发送报文服务实现
/// </summary>
internal sealed class CanSendMessageService : BaseService<CanSendMessage>, ICanSendMessageService
{
    /// <summary>
    /// 根据ID获取CAN发送报文
    /// </summary>
    /// <param name="id">发送报文ID</param>
    /// <returns>CAN发送报文</returns>
    public async Task<CanSendMessage?> GetCanSendMessageByIdAsync(long id)
    {
        using var db = GetDB();
        return await db.Queryable<CanSendMessage>().FirstAsync(x => x.Id == id).ConfigureAwait(false);
    }

    /// <summary>
    /// 根据CanBms ID获取所有CAN发送报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN发送报文列表</returns>
    public async Task<List<CanSendMessage>> GetCanSendMessagesByBmsIdAsync(long canBmsId)
    {
        using var db = GetDB();
        return await db.Queryable<CanSendMessage>()
            .Where(x => x.CanBmsId == canBmsId)
            .OrderBy(x => x.Priority)
            .ThenBy(x => x.MessageId)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// 根据CanBms ID获取启用的CAN发送报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>启用的CAN发送报文列表</returns>
    public async Task<List<CanSendMessage>> GetEnabledCanSendMessagesByBmsIdAsync(long canBmsId)
    {
        using var db = GetDB();
        return await db.Queryable<CanSendMessage>()
            .Where(x => x.CanBmsId == canBmsId && x.SendEnable)
            .OrderBy(x => x.Priority)
            .ThenBy(x => x.MessageId)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    /// <summary>
    /// 创建CAN发送报文
    /// </summary>
    /// <param name="canSendMessage">CAN发送报文</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateCanSendMessageAsync(CanSendMessage canSendMessage)
    {
        using var db = GetDB();
        var result = await db.Insertable(canSendMessage).ExecuteCommandAsync().ConfigureAwait(false);
        return result > 0;
    }

    /// <summary>
    /// 更新CAN发送报文
    /// </summary>
    /// <param name="canSendMessage">CAN发送报文</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateCanSendMessageAsync(CanSendMessage canSendMessage)
    {
        using var db = GetDB();
        var result = await db.Updateable(canSendMessage).ExecuteCommandAsync().ConfigureAwait(false);
        return result > 0;
    }

    /// <summary>
    /// 删除CAN发送报文
    /// </summary>
    /// <param name="id">发送报文ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanSendMessageAsync(long id)
    {
        using var db = GetDB();
        var result = await db.Deleteable<CanSendMessage>().Where(x => x.Id == id).ExecuteCommandAsync().ConfigureAwait(false);
        return result > 0;
    }

    /// <summary>
    /// 批量创建CAN发送报文
    /// </summary>
    /// <param name="canSendMessages">CAN发送报文列表</param>
    /// <returns>创建结果</returns>
    public async Task<bool> BatchCreateCanSendMessagesAsync(List<CanSendMessage> canSendMessages)
    {
        if (canSendMessages?.Count == 0)
            return true;

        using var db = GetDB();
        var result = await db.Insertable(canSendMessages).ExecuteCommandAsync().ConfigureAwait(false);
        return result > 0;
    }

    /// <summary>
    /// 批量删除CAN发送报文
    /// </summary>
    /// <param name="ids">发送报文ID列表</param>
    /// <returns>删除结果</returns>
    public async Task<bool> BatchDeleteCanSendMessagesAsync(List<long> ids)
    {
        if (ids?.Count == 0)
            return true;

        using var db = GetDB();
        var result = await db.Deleteable<CanSendMessage>().Where(x => ids.Contains(x.Id)).ExecuteCommandAsync().ConfigureAwait(false);
        return result > 0;
    }
}

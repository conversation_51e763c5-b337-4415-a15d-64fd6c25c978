﻿
using BootstrapBlazor.Components;

using Mapster;

using System.Collections.Concurrent;

using Matrix.Extension;
using Matrix.NewLife.Extension;
using Matrix.NewLife.Threading;

namespace Matrix.Aging.Application;

/// <summary>
/// 业务设备运行状态
/// </summary>
public class DeviceRuntime : Device, IDisposable
{
    protected volatile DeviceStatusEnum _deviceStatus = DeviceStatusEnum.Default;

    private string? _lastErrorMessage;

    /// <summary>
    /// 设备活跃时间
    /// </summary>
    public DateTime ActiveTime { get; set; } = DateTime.UnixEpoch.ToLocalTime();

    /// <summary>
    /// 插件名称
    /// </summary>
    public virtual string PluginName => ChannelRuntime?.PluginName;

    /// <summary>
    /// 插件名称
    /// </summary>
    [AutoGenerateColumn(Ignore = true)]
    public virtual PluginTypeEnum? PluginType => ChannelRuntime?.PluginInfo?.PluginType;

    /// <summary>
    /// 是否采集
    /// </summary>
    [AutoGenerateColumn(Ignore = true)]
    public bool? IsCollect => PluginType == null ? null : PluginType == PluginTypeEnum.Collect;

    /// <summary>
    /// 是否老化
    /// </summary>
    [AutoGenerateColumn(Ignore = true)]
    public bool? IsAging { get; set; }

    /// <summary>
    /// 通道
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public ChannelRuntime? ChannelRuntime { get; set; }

    /// <summary>
    /// 通道名称
    /// </summary>
    public string? ChannelName => ChannelRuntime?.Name;
    [AutoGenerateColumn(Ignore = true)]
    public string LogPath => Name.GetDeviceLogPath();

    /// <summary>
    /// 设备状态
    /// </summary>
    public virtual DeviceStatusEnum DeviceStatus
    {
        get
        {
            if (!Pause)
                return _deviceStatus;
            else
                return DeviceStatusEnum.Pause;
        }
        set
        {
            lock (this)
            {
                if (_deviceStatus != value)
                {
                    _deviceStatus = value;
                    GlobalData.DeviceStatusChange(this);
                }
            }
        }
    }


    /// <summary>
    /// 暂停
    /// </summary>
    public bool Pause { get; set; } = false;

    /// <summary>
    /// 最后一次失败原因
    /// </summary>
    public string? LastErrorMessage
    {
        get
        {
            return _lastErrorMessage;
        }
        set
        {
            if (!value.IsNullOrWhiteSpace())
                _lastErrorMessage = TimerX.Now.ToDefaultDateTimeFormat() + " - " + value;
        }
    }

    /// <summary>
    /// 设备属性数量
    /// </summary>
    [AutoGenerateColumn(Ignore = true)]
    public int PropertysCount { get => DevicePropertys == null ? 0 : DevicePropertys.Count; }

    #region 老化测试相关属性

    /// <summary>
    /// 老化方案运行时状态
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public AgSchemeRuntime? AgingRuntime { get; set; }

    /// <summary>
    /// 是否为老化测试设备
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public bool IsAgingDevice => ChannelRuntime?.IsAging == true;

    #endregion

    /// <summary>
    /// 设备变量数量
    /// </summary>
    public int DeviceVariableCount { get => Driver == null ? VariableRuntimes?.Count ?? 0 : Driver.IdVariableRuntimes.Count; }



    #region 采集


    /// <summary>
    /// 设备变量
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public IReadOnlyDictionary<string, VariableRuntime>? ReadOnlyVariableRuntimes => VariableRuntimes;

    /// <summary>
    /// 设备变量
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    internal ConcurrentDictionary<string, VariableRuntime>? VariableRuntimes { get; } = new(Environment.ProcessorCount, 1000);

    /// <summary>
    /// 特殊方法数量
    /// </summary>
    public int MethodVariableCount { get; set; }

    /// <summary>
    /// 特殊方法变量
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public List<VariableMethod>? ReadVariableMethods { get; set; }

    /// <summary>
    /// 设备读取打包数量
    /// </summary>
    public int SourceVariableCount => VariableSourceReads?.Count ?? 0;

    /// <summary>
    /// 打包变量
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public List<VariableSourceRead>? VariableSourceReads { get; set; }

    /// <summary>
    /// 特殊地址变量
    /// </summary>
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public List<VariableScriptRead>? VariableScriptReads { get; set; }


    public volatile bool CheckEnable;

    #endregion 采集

    /// <summary>
    /// 传入设备的状态信息
    /// </summary>
    /// <param name="activeTime"></param>
    /// <param name="error"></param>
    /// <param name="lastErrorMessage"></param>
    public void SetDeviceStatus(DateTime? activeTime = null, bool? error = null, string lastErrorMessage = null)
    {
        if (activeTime != null)
            ActiveTime = activeTime.Value;
        if (error == true)
        {
            DeviceStatus = DeviceStatusEnum.OffLine;
        }
        else if (error == false)
        {
            DeviceStatus = DeviceStatusEnum.OnLine;
        }
        if (lastErrorMessage != null)
            LastErrorMessage = lastErrorMessage;
    }



    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    [AdaptIgnore]
    [AutoGenerateColumn(Ignore = true)]
    public IDriver? Driver { get; internal set; }



    public void Init(ChannelRuntime channelRuntime)
    {
        ChannelRuntime?.DeviceRuntimes?.TryRemove(Id, out _);

        ChannelRuntime = channelRuntime;
        ChannelRuntime?.DeviceRuntimes?.TryRemove(Id, out _);
        ChannelRuntime.DeviceRuntimes.TryAdd(Id, this);

        GlobalData.IdDevices.TryRemove(Id, out _);
        GlobalData.IdDevices.TryAdd(Id, this);
        GlobalData.Devices.TryRemove(Name, out _);
        GlobalData.Devices.TryAdd(Name, this);

        // 初始化老化测试
        if (IsAgingDevice)
        {
            InitializeAgingTest();
        }
    }

    /// <summary>
    /// 初始化老化测试
    /// </summary>
    private void InitializeAgingTest()
    {
        try
        {
            var agingTestService = App.GetService<IAgingTestService>();
            if (agingTestService != null)
            {
                _ = Task.Run(async () => await agingTestService.InitializeAgingTest(this).ConfigureAwait(false));
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不影响设备初始化
            LastErrorMessage = $"初始化老化测试失败: {ex.Message}";
        }
    }

    public void Dispose()
    {
        // 清理老化测试资源
        if (IsAgingDevice)
        {
            try
            {
                var agingTestService = App.GetService<IAgingTestService>();
                if (agingTestService != null)
                {
                    _ = Task.Run(async () => await agingTestService.CleanupAgingTest(Name).ConfigureAwait(false));
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响设备释放
                LastErrorMessage = $"清理老化测试资源失败: {ex.Message}";
            }
        }

        ChannelRuntime?.DeviceRuntimes?.TryRemove(Id, out _);

        GlobalData.IdDevices.TryRemove(Id, out _);
        GlobalData.Devices.TryRemove(Name, out _);

        Driver = null;
        VariableSourceReads?.Clear();
        VariableScriptReads?.Clear();
        ReadVariableMethods?.Clear();

        GC.SuppressFinalize(this);
    }

    #region CAN数据访问方法

    /// <summary>
    /// 获取CAN信号值（仅当设备为CAN类型时可用）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果不存在返回null</returns>
    public double? GetCanSignalValue(string signalName)
    {
        // CAN信号值现在通过CAN插件的CanDeviceRuntime获取
        // 这个方法保留用于向后兼容，但建议直接使用CAN插件的API
        if (ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN && Driver != null)
        {
            // 尝试通过反射调用CAN插件的GetCanSignalValue方法
            var method = Driver.GetType().GetMethod("GetCanSignalValue");
            if (method != null)
            {
                var result = method.Invoke(Driver, new object[] { signalName });
                return result as double?;
            }
        }
        return null;
    }

    /// <summary>
    /// 设置CAN信号值并发送（仅当设备为CAN类型时可用）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>发送结果</returns>
    public async Task<bool> SetCanSignalValueAsync(string signalName, double value)
    {
        // CAN信号设置现在通过CAN插件的WriteValuesAsync方法实现
        if (ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN && Driver is CollectBase collectDriver)
        {
            try
            {
                // 构造CAN信号写入格式：Signal1=value
                var signalWriteRequest = $"{signalName}={value}";

                // 通过反射调用CAN插件的WriteValuesAsync方法
                var writeMethod = Driver.GetType().GetMethod("WriteValuesAsync");
                if (writeMethod != null)
                {
                    // 创建写入数据字典，使用信号名称作为键，值作为JToken
                    var writeData = new Dictionary<string, object>
                    {
                        { signalName, value }
                    };

                    // 调用插件的写方法
                    var task = writeMethod.Invoke(Driver, new object[] { writeData, CancellationToken.None });
                    if (task is Task<Dictionary<string, object>> resultTask)
                    {
                        var results = await resultTask;
                        return results.ContainsKey(signalName);
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                // LogMessage?.LogError(ex, $"CAN信号写入失败: {signalName}={value}");
                return false;
            }
        }
        return false;
    }

    /// <summary>
    /// 批量获取CAN信号值（仅当设备为CAN类型时可用）
    /// </summary>
    /// <param name="signalNames">信号名称列表</param>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetCanSignalValues(params string[] signalNames)
    {
        var result = new Dictionary<string, double>();

        if (ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN)
        {
            foreach (var signalName in signalNames)
            {
                var value = GetCanSignalValue(signalName);
                if (value.HasValue)
                {
                    result[signalName] = value.Value;
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 批量设置CAN信号值并发送（仅当设备为CAN类型时可用）
    /// </summary>
    /// <param name="signalValues">信号值字典</param>
    /// <returns>发送结果</returns>
    public async Task<bool> SetCanSignalValuesAsync(Dictionary<string, double> signalValues)
    {
        // CAN信号设置现在通过插件的写方法实现
        if (ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN && Driver is CollectBase collectDriver)
        {
            // LogMessage?.LogWarning("SetCanSignalValuesAsync已废弃，请使用CAN插件的写方法");
            return false;
        }
        return false;
    }

    /// <summary>
    /// 检查是否为CAN设备
    /// </summary>
    /// <returns>是否为CAN设备</returns>
    public bool IsCanDevice()
    {
        return ChannelRuntime?.ChannelType == ChannelTypeEnum.CAN;
    }

    /// <summary>
    /// 获取变量值（统一API，自动选择数据源）
    /// </summary>
    /// <param name="name">变量名称</param>
    /// <returns>变量值</returns>
    public object? GetVariableValue(string name)
    {
        // CAN设备：优先从CAN信号获取，如果没有则从Variable系统获取
        if (IsCanDevice())
        {
            var canValue = GetCanSignalValue(name);
            if (canValue.HasValue)
            {
                return canValue.Value;
            }

            // 如果CAN信号中没有找到，再尝试Variable系统（用于脚本变量等）
            var variable = VariableRuntimes?.Values.FirstOrDefault(a => a.Name == name);
            return variable?.Value;
        }

        // 非CAN设备：直接从Variable系统获取
        var variableRuntime = VariableRuntimes?.Values.FirstOrDefault(a => a.Name == name);
        return variableRuntime?.Value;
    }

    /// <summary>
    /// 设置变量值（统一API，自动选择目标系统）
    /// </summary>
    /// <param name="name">变量名称</param>
    /// <param name="value">变量值</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetVariableValueAsync(string name, object value)
    {
        // CAN设备：优先尝试设置CAN信号
        if (IsCanDevice())
        {
            // 尝试转换为double类型（CAN信号值）
            if (value is double doubleValue)
            {
                return await SetCanSignalValueAsync(name, doubleValue);
            }
            else if (double.TryParse(value?.ToString(), out var parsedValue))
            {
                return await SetCanSignalValueAsync(name, parsedValue);
            }

            // 如果不是数值类型，尝试设置Variable（用于脚本变量等）
            var variable = VariableRuntimes?.Values.FirstOrDefault(a => a.Name == name);
            if (variable != null)
            {
                var result = variable.SetValue(value, DateTime.Now);
                return result.IsSuccess;
            }

            return false;
        }

        // 非CAN设备：直接使用Variable系统
        var variableRuntime = VariableRuntimes?.Values.FirstOrDefault(a => a.Name == name);
        if (variableRuntime != null)
        {
            var result = variableRuntime.SetValue(value, DateTime.Now);
            return result.IsSuccess;
        }

        return false;
    }

    #endregion CAN数据访问方法

}

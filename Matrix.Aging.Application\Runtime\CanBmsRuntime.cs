using Matrix.Foundation;
using System.Collections.Concurrent;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN BMS运行时管理类
/// </summary>
public class CanBmsRuntime
{
    /// <summary>
    /// CanBms配置
    /// </summary>
    public CanBms CanBms { get; private set; }

    /// <summary>
    /// 所有CanMessage运行时（按MessageId索引）
    /// </summary>
    public ConcurrentDictionary<uint, CanMessageRuntime> MessageRuntimes { get; private set; } = new();

    /// <summary>
    /// 启用发送的Message列表
    /// </summary>
    public List<CanMessageRuntime> SendEnabledMessages { get; private set; } = new();

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastReceiveTime { get; set; }

    /// <summary>
    /// 接收计数
    /// </summary>
    public long ReceiveCount { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="canBms">CanBms配置</param>
    public CanBmsRuntime(CanBms canBms)
    {
        CanBms = canBms;
    }

    /// <summary>
    /// 初始化BMS运行时
    /// </summary>
    /// <param name="canMessages">CAN报文列表</param>
    /// <param name="canSignalService">CAN信号服务</param>
    public async Task InitializeAsync(List<CanMessage> canMessages, ICanSignalService canSignalService)
    {
        MessageRuntimes.Clear();
        SendEnabledMessages.Clear();

        foreach (var canMessage in canMessages.Where(m => m.Enable))
        {
            // 创建Message运行时
            var messageRuntime = new CanMessageRuntime(canMessage);

            // 获取关联的信号
            var signals = await canSignalService.GetCanSignalsByCanMessageIdAsync(canMessage.Id).ConfigureAwait(false);

            // 初始化Message运行时
            messageRuntime.Initialize(signals);

            // 添加到映射
            MessageRuntimes[canMessage.MessageId] = messageRuntime;

            // 如果是发送报文，添加到发送列表
            if (canMessage.MessageType == CanMessageTypeEnum.Send)
            {
                SendEnabledMessages.Add(messageRuntime);
            }
        }
    }

    /// <summary>
    /// 处理接收到的CAN帧
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>是否处理成功</returns>
    public bool ProcessCanFrame(uint canId, ReadOnlySpan<byte> canData, DateTime timestamp)
    {
        if (MessageRuntimes.TryGetValue(canId, out var messageRuntime))
        {
            try
            {
                messageRuntime.ParseCanData(canData, timestamp);
                LastReceiveTime = timestamp;
                ReceiveCount++;
                IsOnline = true;
                return true;
            }
            catch
            {
                return false;
            }
        }

        return false; // 未找到对应的Message
    }

    /// <summary>
    /// 构建发送数据
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值字典</param>
    /// <returns>CAN数据</returns>
    public byte[]? BuildSendData(uint canId, Dictionary<string, double> signalValues)
    {
        var messageRuntime = SendEnabledMessages.FirstOrDefault(m => m.CanMessage.MessageId == canId);
        return messageRuntime?.BuildSendData(signalValues);
    }

    /// <summary>
    /// 获取所有信号值
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetAllSignalValues()
    {
        var result = new Dictionary<string, double>();
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            var messageValues = messageRuntime.GetAllSignalValues();
            foreach (var kvp in messageValues)
            {
                result[kvp.Key] = kvp.Value;
            }
        }
        return result;
    }

    /// <summary>
    /// 获取指定信号的值
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果不存在或离线返回null</returns>
    public double? GetSignalValue(string signalName)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            var value = messageRuntime.GetSignalValue(signalName);
            if (value.HasValue)
            {
                return value;
            }
        }
        return null;
    }

    /// <summary>
    /// 检查是否包含指定信号
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>是否包含该信号</returns>
    public bool HasSignal(string signalName)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            if (messageRuntime.HasSignal(signalName))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 检查信号是否在线
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>是否在线</returns>
    public bool IsSignalOnline(string signalName)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            if (messageRuntime.HasSignal(signalName))
            {
                return messageRuntime.IsSignalOnline(signalName);
            }
        }
        return false;
    }

    /// <summary>
    /// 设置信号值
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否设置成功</returns>
    public bool SetSignalValue(string signalName, double value)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            if (messageRuntime.HasSignal(signalName))
            {
                return messageRuntime.SetSignalValue(signalName, value);
            }
        }
        return false;
    }

    /// <summary>
    /// 设置信号值并构建发送数据
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>发送数据结果(CAN ID, Data)</returns>
    public (uint canId, byte[] data)? SetSignalValueAndBuildSendData(string signalName, double value)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            if (messageRuntime.HasSignal(signalName))
            {
                // 设置信号值
                if (messageRuntime.SetSignalValue(signalName, value))
                {
                    // 构建发送数据
                    var data = messageRuntime.BuildCurrentSendData();
                    return (messageRuntime.CanMessage.MessageId, data);
                }
            }
        }
        return null;
    }

    /// <summary>
    /// 获取指定报文的MessageRuntime
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <returns>MessageRuntime</returns>
    public CanMessageRuntime? GetMessageRuntime(uint canId)
    {
        return MessageRuntimes.TryGetValue(canId, out var messageRuntime) ? messageRuntime : null;
    }

    /// <summary>
    /// 获取包含指定信号的MessageRuntime
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>MessageRuntime</returns>
    public CanMessageRuntime? GetMessageRuntimeBySignal(string signalName)
    {
        foreach (var messageRuntime in MessageRuntimes.Values)
        {
            if (messageRuntime.HasSignal(signalName))
            {
                return messageRuntime;
            }
        }
        return null;
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public CanBmsStatistics GetStatistics()
    {
        var totalSignalCount = MessageRuntimes.Values.Sum(m => m.Signals.Length);
        var onlineSignalCount = MessageRuntimes.Values.Sum(m => m.SignalOnlineStatus.Count(s => s));
        
        return new CanBmsStatistics
        {
            BmsName = CanBms.BmsName,
            LastReceiveTime = LastReceiveTime,
            ReceiveCount = ReceiveCount,
            IsOnline = IsOnline,
            TotalSignalCount = totalSignalCount,
            OnlineSignalCount = onlineSignalCount,
            SignalOnlineRate = totalSignalCount > 0 ? (double)onlineSignalCount / totalSignalCount : 0,
            TotalMessageCount = MessageRuntimes.Count,
            OnlineMessageCount = MessageRuntimes.Values.Count(m => m.IsOnline)
        };
    }
}

/// <summary>
/// CAN BMS统计信息
/// </summary>
public class CanBmsStatistics
{
    /// <summary>
    /// BMS名称
    /// </summary>
    public string BmsName { get; set; } = string.Empty;

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastReceiveTime { get; set; }

    /// <summary>
    /// 接收计数
    /// </summary>
    public long ReceiveCount { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 总报文数量
    /// </summary>
    public int TotalMessageCount { get; set; }

    /// <summary>
    /// 在线报文数量
    /// </summary>
    public int OnlineMessageCount { get; set; }

    /// <summary>
    /// 总信号数量
    /// </summary>
    public int TotalSignalCount { get; set; }

    /// <summary>
    /// 在线信号数量
    /// </summary>
    public int OnlineSignalCount { get; set; }

    /// <summary>
    /// 信号在线率
    /// </summary>
    public double SignalOnlineRate { get; set; }

    /// <summary>
    /// 报文在线率
    /// </summary>
    public double MessageOnlineRate => TotalMessageCount > 0 ? (double)OnlineMessageCount / TotalMessageCount : 0;
}

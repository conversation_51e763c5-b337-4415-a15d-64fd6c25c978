﻿<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="..\Foundation.props" />
	
	<PropertyGroup>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="9.0.4" />
		<PackageReference Include="SocketCANSharp" Version="0.13.0" />
		<PackageReference Include="TouchSocket" Version="3.0.26" />
		<PackageReference Include="TouchSocket.SerialPorts" Version="3.0.26" />
		<!--<PackageReference Include="TouchSocket" Version="3.1.2" />
		<PackageReference Include="TouchSocket.SerialPorts" Version="3.1.2" />-->
	</ItemGroup>

	<ItemGroup>
		<Content Remove="Locales\*.json" />
		<EmbeddedResource Include="Locales\*.json">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Matrix.NewLife.X\Matrix.NewLife.X.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Bms\" />
	</ItemGroup>
	
</Project>

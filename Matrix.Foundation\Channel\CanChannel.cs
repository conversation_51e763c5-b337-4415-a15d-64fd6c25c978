﻿using Matrix.NewLife;
using Newtonsoft.Json;
using SocketCANSharp;
using SocketCANSharp.Network;
using SocketCANSharp.Network.Netlink;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Net;
using System.Reflection;

namespace Matrix.Foundation;

/// <summary>
/// CAN通道
/// </summary>
public class CanChannel : CanClient, IClientChannel
{
    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="channelOptions">通道配置</param>
    public CanChannel(IChannelOptions channelOptions)
    {
        ChannelOptions = channelOptions;
        WaitHandlePool.MaxSign = ushort.MaxValue;

        // 将配置传递给基类
        this.SetChannelOptions(channelOptions);
    }

    #endregion

    #region IClientChannel 属性实现

    /// <inheritdoc/>
    public override TouchSocketConfig Config => base.Config ?? ChannelOptions.Config;

    /// <inheritdoc/>
    public int MaxSign { get => WaitHandlePool.MaxSign; set => WaitHandlePool.MaxSign = value; }

    /// <inheritdoc/>
    public ChannelReceivedEventHandler ChannelReceived { get; set; } = new();

    /// <inheritdoc/>
    public IChannelOptions ChannelOptions { get; }

    /// <inheritdoc/>
    public ChannelTypeEnum ChannelType => ChannelOptions.ChannelType;

    /// <inheritdoc/>
    public ConcurrentList<IDevice> Collects { get; } = new();

    /// <inheritdoc/>
    public DataHandlingAdapter ReadOnlyDataHandlingAdapter => null; // CAN通道不使用数据处理适配器

    /// <inheritdoc/>
    public ChannelEventHandler Started { get; set; } = new();

    /// <inheritdoc/>
    public ChannelEventHandler Starting { get; set; } = new();

    /// <inheritdoc/>
    public bool IsAging => ChannelOptions.IsAging;

    /// <inheritdoc/>
    public ChannelEventHandler Stoped { get; set; } = new();

    /// <inheritdoc/>
    public ChannelEventHandler Stoping { get; set; } = new();

    /// <summary>
    /// 等待池
    /// </summary>
    public WaitHandlePool<MessageBase> WaitHandlePool { get; } = new();

    /// <inheritdoc/>
    public WaitLock WaitLock => ChannelOptions.WaitLock;

    /// <inheritdoc/>
    public virtual WaitLock GetLock(string key) => WaitLock;

    /// <inheritdoc/>
    public ConcurrentDictionary<long, Func<IClientChannel, ReceivedDataEventArgs, bool, Task>> ChannelReceivedWaitDict { get; } = new();

    #endregion

    #region IClientChannel 方法实现

    /// <inheritdoc/>
    public void SetDataHandlingAdapter(DataHandlingAdapter adapter)
    {
        // CAN通道不使用数据处理适配器，此方法为空实现
    }

    #endregion

    #region 连接和断开重写

    /// <inheritdoc/>
    public override async Task ConnectAsync(int millisecondsTimeout, CancellationToken token)
    {
        if (!Online)
        {
            try
            {
                if (!Online)
                {
                    if (token.IsCancellationRequested) return;
                    await base.ConnectAsync(millisecondsTimeout, token).ConfigureAwait(false);
                    if (Online)
                    {
                        if (token.IsCancellationRequested) return;
                        
                        // CAN通道只负责硬件连接，数据处理由设备插件管理
                        Logger?.Info("CAN通道连接成功，等待设备插件注册数据处理器");
                        
                        await this.OnChannelEvent(Started).ConfigureAwait(false);
                    }
                }
            }
            catch
            {
                throw;
            }
        }
    }

    /// <inheritdoc/>
    public override async Task CloseAsync(string msg)
    {
        if (Online)
        {
            try
            {
                if (Online)
                {
                    await base.CloseAsync(msg).ConfigureAwait(false);
                    if (!Online)
                    {
                        await this.OnChannelEvent(Stoped).ConfigureAwait(false);
                    }
                }
            }
            catch
            {
                throw;
            }
        }
    }

    #endregion

    #region CAN事件处理重写

    /// <inheritdoc/>
    protected override async Task OnCanConnecting(ConnectingEventArgs e)
    {
        Logger?.Trace($"{ToString()} CAN Connecting{(e.Message.IsNullOrEmpty() ? string.Empty : $" -{e.Message}")}");
        await this.OnChannelEvent(Starting).ConfigureAwait(false);
        await base.OnCanConnecting(e).ConfigureAwait(false);
    }

    /// <inheritdoc/>
    protected override async Task OnCanConnected(ConnectedEventArgs e)
    {
        Logger?.Debug($"{ToString()} CAN Connected");
        await base.OnCanConnected(e).ConfigureAwait(false);
    }

    /// <inheritdoc/>
    protected override async Task OnCanClosing(ClosingEventArgs e)
    {
        await this.OnChannelEvent(Stoping).ConfigureAwait(false);
        Logger?.Trace($"{ToString()} CAN Closing{(e.Message.IsNullOrEmpty() ? string.Empty : $" -{e.Message}")}");
        await base.OnCanClosing(e).ConfigureAwait(false);
    }

    /// <inheritdoc/>
    protected override async Task OnCanClosed(ClosedEventArgs e)
    {
        Logger?.Info($"{ToString()} CAN Closed{(e.Message.IsNullOrEmpty() ? string.Empty : $" -{e.Message}")}");
        await base.OnCanClosed(e).ConfigureAwait(false);
    }

    /// <inheritdoc/>
    protected override async Task OnCanReceived(CanReceivedEventArgs e)
    {
        await base.OnCanReceived(e).ConfigureAwait(false);

        // 将CAN帧转换为ReceivedDataEventArgs格式
        if (e.Frame != null)
        {
            // 创建一个简单的消息包装器来处理CAN帧
            var canMessage = new CanMessage(e.Frame);

            // 创建ByteBlock来包装CAN数据
            using var byteBlock = new ByteBlock(e.Frame.Data);
            var receivedArgs = new ReceivedDataEventArgs(byteBlock, canMessage);

            // CAN通道特殊处理：直接解析变量
            await ProcessCanFrameForDevices(e.Frame).ConfigureAwait(false);

            // 检查是否有等待的响应处理器
            if (canMessage is MessageBase response)
            {
                if (ChannelReceivedWaitDict.TryRemove(response.Sign, out var func))
                {
                    await func.Invoke(this, receivedArgs, ChannelReceived.Count == 1).ConfigureAwait(false);
                    receivedArgs.Handled = true;
                }
            }

            if (!receivedArgs.Handled)
            {
                await this.OnChannelReceivedEvent(receivedArgs, ChannelReceived).ConfigureAwait(false);
            }
        }
    }

    /// <summary>
    /// CAN帧处理器委托（使用ReadOnlySpan提高性能）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>是否处理成功</returns>
    public delegate bool CanFrameProcessor(uint canId, ReadOnlySpan<byte> canData, DateTime timestamp);

    /// <summary>
    /// CAN帧处理器数组（使用数组提高遍历性能）
    /// </summary>
    private CanFrameProcessor[] _canFrameProcessors = Array.Empty<CanFrameProcessor>();

    /// <summary>
    /// 处理器锁
    /// </summary>
    private readonly object _processorLock = new object();

    /// <summary>
    /// 保留字段以兼容现有代码，但不再主动初始化
    /// </summary>
    private object? _canBmsRuntime = null;

    /// <summary>
    /// 保留锁对象以兼容现有代码
    /// </summary>
    private readonly object _canBmsLock = new object();

    /// <summary>
    /// 性能优化：缓存的反射方法委托
    /// </summary>
    private static readonly ConcurrentDictionary<string, Delegate> _methodCache = new();

    /// <summary>
    /// 性能优化：ProcessCanFrame方法的委托类型
    /// </summary>
    private delegate bool ProcessCanFrameDelegate(object runtime, uint canId, byte[] canData, DateTime timestamp);

    /// <summary>
    /// 性能优化：缓存的ProcessCanFrame委托
    /// </summary>
    private ProcessCanFrameDelegate? _cachedProcessCanFrameDelegate;

    /// <summary>
    /// 性能优化：常用API方法的委托类型
    /// </summary>
    private delegate double? GetSignalValueDelegate(object runtime, string signalName);
    private delegate bool SetSignalValueDelegate(object runtime, string signalName, double value);
    private delegate object SetSignalValueAndBuildSendDataDelegate(object runtime, string signalName, double value);
    private delegate Dictionary<string, double> GetAllSignalValuesDelegate(object runtime);

    /// <summary>
    /// 性能优化：缓存的常用API委托
    /// </summary>
    private GetSignalValueDelegate? _cachedGetSignalValueDelegate;
    private SetSignalValueDelegate? _cachedSetSignalValueDelegate;
    private SetSignalValueAndBuildSendDataDelegate? _cachedSetSignalValueAndBuildSendDataDelegate;
    private GetAllSignalValuesDelegate? _cachedGetAllSignalValuesDelegate;

    /// <summary>
    /// 注册CAN帧处理器
    /// </summary>
    /// <param name="processor">处理器</param>
    public void RegisterCanFrameProcessor(CanFrameProcessor processor)
    {
        lock (_processorLock)
        {
            var newArray = new CanFrameProcessor[_canFrameProcessors.Length + 1];
            Array.Copy(_canFrameProcessors, newArray, _canFrameProcessors.Length);
            newArray[_canFrameProcessors.Length] = processor;
            _canFrameProcessors = newArray;
        }
    }

    /// <summary>
    /// 注销CAN帧处理器
    /// </summary>
    /// <param name="processor">处理器</param>
    public void UnregisterCanFrameProcessor(CanFrameProcessor processor)
    {
        lock (_processorLock)
        {
            var index = Array.IndexOf(_canFrameProcessors, processor);
            if (index >= 0)
            {
                var newArray = new CanFrameProcessor[_canFrameProcessors.Length - 1];
                Array.Copy(_canFrameProcessors, 0, newArray, 0, index);
                Array.Copy(_canFrameProcessors, index + 1, newArray, index, _canFrameProcessors.Length - index - 1);
                _canFrameProcessors = newArray;
            }
        }
    }

    /// <summary>
    /// 初始化CAN BMS运行时（已简化：不再主动初始化，由设备插件管理）
    /// </summary>
    /// <returns></returns>
    private async Task InitializeCanBmsRuntimeAsync()
    {
        // 新架构：Channel不再主动初始化CanBmsRuntime
        // 所有数据处理由设备插件通过RegisterCanFrameProcessor注册处理器来完成
        Logger?.Info("CAN通道采用新架构：数据处理由设备插件管理，Channel仅负责硬件连接");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理接收到的CAN帧，直接解析到相关设备的变量中
    /// </summary>
    /// <param name="frame">CAN帧</param>
    private async Task ProcessCanFrameForDevices(CanFrame frame)
    {
        if (frame?.Data == null || frame.Data.Length == 0)
            return;

        var canId = frame.Id;
        var canData = frame.Data;
        var timestamp = DateTime.Now;

        try
        {
            // 获取注册的处理器数组的本地副本，避免长时间持锁
            var processors = _canFrameProcessors;

            // 调用所有注册的设备插件处理器
            for (int i = 0; i < processors.Length; i++)
            {
                try
                {
                    var processed = processors[i](canId, canData.AsSpan(), timestamp);
                    // 注意：所有处理器都会被调用，不会因为一个处理器处理成功就停止
                }
                catch (Exception ex)
                {
                    Logger?.Warning($"CAN帧处理器执行失败: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Error($"处理CAN帧时发生错误: CAN ID=0x{canId:X8}, 错误={ex.Message}");
        }

        await Task.CompletedTask.ConfigureAwait(false);
    }

    #endregion

    #region 资源清理

    /// <inheritdoc/>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            try
            {
                // 确保在释放时关闭连接
                if (Online)
                {
                    CloseAsync("应用程序关闭").GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                Logger?.Error($"关闭CAN通道时发生错误: {ex.Message}");
            }

            WaitHandlePool.SafeDispose();
        }
        base.Dispose(disposing);
    }

    #endregion

    #region Device API - 为Device提供的CAN信号操作接口

    /// <summary>
    /// 获取CAN信号值（为Device提供的API）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果信号不存在或离线返回null</returns>
    public async Task<double?> GetCanSignalValueAsync(string signalName)
    {
        return await Task.FromResult(GetCanSignalValue(signalName)).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取CAN信号值（委托给设备插件实现）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果信号不存在或离线返回null</returns>
    public double? GetCanSignalValue(string signalName)
    {
        // 新架构：信号值获取由设备插件实现，Channel层面不再直接管理信号数据
        Logger?.Debug($"GetCanSignalValue方法应由设备插件实现，信号名称: {signalName}");
        return null;
    }

    /// <summary>
    /// 设置CAN信号值（为Device提供的API）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否设置成功</returns>
    public async Task<bool> SetCanSignalValueAsync(string signalName, double value)
    {
        return await Task.FromResult(SetCanSignalValue(signalName, value)).ConfigureAwait(false);
    }

    /// <summary>
    /// 设置CAN信号值（委托给设备插件实现）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否设置成功</returns>
    public bool SetCanSignalValue(string signalName, double value)
    {
        // 新架构：信号值设置由设备插件实现
        Logger?.Debug($"SetCanSignalValue方法应由设备插件实现，信号名称: {signalName}, 值: {value}");
        return false;
    }

    /// <summary>
    /// 设置CAN信号值并发送报文（为Device提供的API）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否成功</returns>
    public async Task<bool> SetCanSignalValueAndSendAsync(string signalName, double value)
    {
        try
        {
            var canBmsRuntime = _canBmsRuntime;
            if (canBmsRuntime != null)
            {
                // 性能优化：优先使用缓存的委托
                var setAndBuildDelegate = _cachedSetSignalValueAndBuildSendDataDelegate;
                if (setAndBuildDelegate != null)
                {
                    var result = setAndBuildDelegate(canBmsRuntime, signalName, value);
                    if (result != null)
                    {
                        return await ProcessSendDataResult(result).ConfigureAwait(false);
                    }
                }
                else
                {
                    // 兜底：使用反射调用
                    var runtimeType = canBmsRuntime.GetType();
                    var setAndBuildMethod = runtimeType.GetMethod("SetSignalValueAndBuildSendData", new[] { typeof(string), typeof(double) });
                    
                    if (setAndBuildMethod != null)
                    {
                        var result = setAndBuildMethod.Invoke(canBmsRuntime, new object[] { signalName, value });
                        if (result != null)
                        {
                            return await ProcessSendDataResult(result).ConfigureAwait(false);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"设置CAN信号值并发送失败: {signalName}={value}, 错误: {ex.Message}");
        }
        return false;
    }

    /// <summary>
    /// 发送CAN帧（为Device提供的API）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">数据</param>
    /// <returns>是否发送成功</returns>
    public async Task<bool> SendCanFrameAsync(uint canId, byte[] data)
    {
        try
        {
            if (!Online)
            {
                Logger?.Warning("CAN通道未连接，无法发送数据");
                return false;
            }

            // 创建CAN帧
            var frame = new CanFrame
            {
                Id = canId,
                Data = data,
                IsExtended = canId > 0x7FF, // 根据ID大小自动判断
                IsRemote = false,
                IsCanFd = ChannelOptions?.IsCanFd ?? false
            };

            // 发送CAN帧
            await SendAsync(frame).ConfigureAwait(false);
            
            Logger?.Trace($"CAN帧发送成功: ID=0x{canId:X8}, Data={data.ToHexString(' ')}");
            return true;
        }
        catch (Exception ex)
        {
            Logger?.Error($"发送CAN帧失败: ID=0x{canId:X8}, 错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取所有信号值（为Device提供的API）
    /// </summary>
    /// <returns>信号值字典</returns>
    public async Task<Dictionary<string, double>> GetAllCanSignalValuesAsync()
    {
        return await Task.FromResult(GetAllCanSignalValues()).ConfigureAwait(false);
    }

    /// <summary>
    /// 获取所有信号值（委托给设备插件实现）
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetAllCanSignalValues()
    {
        // 新架构：信号值获取由设备插件实现
        Logger?.Debug("GetAllCanSignalValues方法应由设备插件实现");
        return new Dictionary<string, double>();
    }

    /// <summary>
    /// 检查信号是否在线（委托给设备插件实现）
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>是否在线</returns>
    public bool IsCanSignalOnline(string signalName)
    {
        // 新架构：信号在线状态检查由设备插件实现
        Logger?.Debug($"IsCanSignalOnline方法应由设备插件实现，信号名称: {signalName}");
        return false;
    }

    #endregion

    #region 新架构支持方法

    /// <summary>
    /// 获取服务提供者（通过反射避免直接依赖）
    /// </summary>
    /// <returns></returns>
    private object? GetServiceProvider()
    {
        try
        {
            // 尝试通过反射获取App.Services
            var appType = Type.GetType("Matrix.NewLife.App, Matrix.NewLife");
            if (appType != null)
            {
                var servicesProperty = appType.GetProperty("Services", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                return servicesProperty?.GetValue(null);
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"获取服务提供者失败: {ex.Message}");
        }
        return null;
    }

    /// <summary>
    /// 获取服务（通过反射）
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="serviceProvider"></param>
    /// <param name="serviceName"></param>
    /// <returns></returns>
    private T? GetService<T>(object serviceProvider, string serviceName)
    {
        try
        {
            var serviceProviderType = serviceProvider.GetType();
            var getServiceMethod = serviceProviderType.GetMethod("GetService", new[] { typeof(Type) });
            
            if (getServiceMethod != null)
            {
                // 根据服务名称查找类型
                var serviceType = FindServiceType(serviceName);
                if (serviceType != null)
                {
                    var result = getServiceMethod.Invoke(serviceProvider, new object[] { serviceType });
                    return (T)result;
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"获取服务 {serviceName} 失败: {ex.Message}");
        }
        return default(T);
    }

    /// <summary>
    /// 查找服务类型
    /// </summary>
    /// <param name="serviceName"></param>
    /// <returns></returns>
    private Type? FindServiceType(string serviceName)
    {
        try
        {
            // 在Matrix.Aging.Application程序集中查找接口
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            foreach (var assembly in assemblies)
            {
                if (assembly.FullName?.Contains("Matrix.Aging.Application") == true)
                {
                    var types = assembly.GetTypes();
                    var interfaceType = types.FirstOrDefault(t => t.IsInterface && t.Name == serviceName);
                    if (interfaceType != null)
                    {
                        return interfaceType;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"查找服务类型 {serviceName} 失败: {ex.Message}");
        }
        return null;
    }

    /// <summary>
    /// 调用服务方法（异步）
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="service"></param>
    /// <param name="methodName"></param>
    /// <param name="parameters"></param>
    /// <returns></returns>
    private async Task<T?> InvokeServiceMethodAsync<T>(object service, string methodName, params object[] parameters)
    {
        try
        {
            var serviceType = service.GetType();
            var method = serviceType.GetMethod(methodName);
            
            if (method != null)
            {
                var result = method.Invoke(service, parameters);
                if (result is Task task)
                {
                    await task.ConfigureAwait(false);
                    
                    // 获取Task的Result属性
                    var resultProperty = task.GetType().GetProperty("Result");
                    if (resultProperty != null)
                    {
                        return (T)resultProperty.GetValue(task);
                    }
                }
                else
                {
                    return (T)result;
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"调用服务方法 {methodName} 失败: {ex.Message}");
        }
        return default(T);
    }

    /// <summary>
    /// 创建CanBmsRuntime
    /// </summary>
    /// <param name="canBms"></param>
    /// <returns></returns>
    private object CreateCanBmsRuntime(object canBms)
    {
        try
        {
            // 通过反射创建CanBmsRuntime
            var runtimeType = Type.GetType("Matrix.Aging.Application.CanBmsRuntime, Matrix.Aging.Application");
            if (runtimeType != null)
            {
                return Activator.CreateInstance(runtimeType, canBms);
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"创建CanBmsRuntime失败: {ex.Message}");
        }
        return null;
    }

    /// <summary>
    /// 初始化BmsRuntime
    /// </summary>
    /// <param name="canBmsRuntime"></param>
    /// <param name="canMessageService"></param>
    /// <param name="canSignalService"></param>
    /// <returns></returns>
    private async Task InitializeBmsRuntimeAsync(object canBmsRuntime, object canMessageService, object canSignalService)
    {
        try
        {
            // 通过反射调用InitializeAsync方法
            var runtimeType = canBmsRuntime.GetType();
            var initMethod = runtimeType.GetMethod("InitializeAsync");
            
            if (initMethod != null)
            {
                // 需要先获取CanBms的Id来获取CanMessage列表
                var canBmsProperty = runtimeType.GetProperty("CanBms");
                var canBms = canBmsProperty?.GetValue(canBmsRuntime);
                var idProperty = canBms?.GetType().GetProperty("Id");
                var canBmsId = idProperty?.GetValue(canBms);

                if (canBmsId != null)
                {
                    // 获取CanMessage列表
                    var canMessages = await InvokeServiceMethodAsync<object>(canMessageService, "GetCanMessagesByCanBmsIdAsync", canBmsId).ConfigureAwait(false);
                    
                    if (canMessages != null)
                    {
                        // 调用InitializeAsync
                        var task = (Task)initMethod.Invoke(canBmsRuntime, new[] { canMessages, canSignalService });
                        await task.ConfigureAwait(false);
                        
                        // 性能优化：初始化完成后创建和缓存高频调用的委托
                        InitializePerformanceDelegates(canBmsRuntime);
                        
                        Logger?.Info("CanBmsRuntime初始化完成");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Error($"初始化BmsRuntime失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 性能优化：初始化高频调用的委托缓存
    /// </summary>
    /// <param name="canBmsRuntime"></param>
    private void InitializePerformanceDelegates(object canBmsRuntime)
    {
        try
        {
            var runtimeType = canBmsRuntime.GetType();
            
            // 1. 缓存ProcessCanFrame方法（最高频）
            CacheProcessCanFrameDelegate(runtimeType, canBmsRuntime);
            
            // 2. 缓存常用的Device API方法
            CacheDeviceApiDelegates(runtimeType, canBmsRuntime);
        }
        catch (Exception ex)
        {
            Logger?.Warning($"初始化性能委托失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 缓存ProcessCanFrame方法委托
    /// </summary>
    private void CacheProcessCanFrameDelegate(Type runtimeType, object canBmsRuntime)
    {
        var cacheKey = $"{runtimeType.FullName}_ProcessCanFrame";
        
        if (!_methodCache.ContainsKey(cacheKey))
        {
            var processFrameMethod = runtimeType.GetMethod("ProcessCanFrame", new[] { typeof(uint), typeof(ReadOnlySpan<byte>), typeof(DateTime) });
            
            if (processFrameMethod != null)
            {
                _cachedProcessCanFrameDelegate = (runtime, canId, canData, timestamp) =>
                {
                    try
                    {
                        var result = processFrameMethod.Invoke(runtime, new object[] { canId, canData, timestamp });
                        return (bool)result;
                    }
                    catch
                    {
                        return false;
                    }
                };
                
                _methodCache.TryAdd(cacheKey, _cachedProcessCanFrameDelegate);
            }
        }
        else
        {
            _cachedProcessCanFrameDelegate = _methodCache[cacheKey] as ProcessCanFrameDelegate;
        }
    }

    /// <summary>
    /// 处理发送数据结果（性能优化辅助方法）
    /// </summary>
    /// <param name="result">反射调用返回的结果</param>
    /// <returns>是否发送成功</returns>
    private async Task<bool> ProcessSendDataResult(object result)
    {
        try
        {
            // 解析返回的Tuple结果
            var resultType = result.GetType();
            var hasValueProperty = resultType.GetProperty("HasValue");
            var valueProperty = resultType.GetProperty("Value");
            
            if (hasValueProperty != null && valueProperty != null)
            {
                var hasValue = (bool)hasValueProperty.GetValue(result);
                if (hasValue)
                {
                    var tupleValue = valueProperty.GetValue(result);
                    var tupleType = tupleValue.GetType();
                    
                    // 获取CAN ID和数据
                    var canIdField = tupleType.GetField("Item1");
                    var dataField = tupleType.GetField("Item2");
                    
                    if (canIdField != null && dataField != null)
                    {
                        var canId = (uint)canIdField.GetValue(tupleValue);
                        var data = (byte[])dataField.GetValue(tupleValue);
                        
                        // 发送CAN帧
                        return await SendCanFrameAsync(canId, data).ConfigureAwait(false);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Warning($"处理发送数据结果失败: {ex.Message}");
        }
        return false;
    }

    /// <summary>
    /// 缓存Device API方法委托
    /// </summary>
    private void CacheDeviceApiDelegates(Type runtimeType, object canBmsRuntime)
    {
        // 缓存GetSignalValue方法
        var getSignalValueMethod = runtimeType.GetMethod("GetSignalValue", new[] { typeof(string) });
        if (getSignalValueMethod != null)
        {
            _cachedGetSignalValueDelegate = (runtime, signalName) =>
            {
                try
                {
                    var result = getSignalValueMethod.Invoke(runtime, new object[] { signalName });
                    return (double?)result;
                }
                catch
                {
                    return null;
                }
            };
        }

        // 缓存SetSignalValue方法
        var setSignalValueMethod = runtimeType.GetMethod("SetSignalValue", new[] { typeof(string), typeof(double) });
        if (setSignalValueMethod != null)
        {
            _cachedSetSignalValueDelegate = (runtime, signalName, value) =>
            {
                try
                {
                    var result = setSignalValueMethod.Invoke(runtime, new object[] { signalName, value });
                    return (bool)result;
                }
                catch
                {
                    return false;
                }
            };
        }

        // 缓存SetSignalValueAndBuildSendData方法
        var setAndBuildMethod = runtimeType.GetMethod("SetSignalValueAndBuildSendData", new[] { typeof(string), typeof(double) });
        if (setAndBuildMethod != null)
        {
            _cachedSetSignalValueAndBuildSendDataDelegate = (runtime, signalName, value) =>
            {
                try
                {
                    return setAndBuildMethod.Invoke(runtime, new object[] { signalName, value });
                }
                catch
                {
                    return null;
                }
            };
        }

        // 缓存GetAllSignalValues方法
        var getAllSignalValuesMethod = runtimeType.GetMethod("GetAllSignalValues");
        if (getAllSignalValuesMethod != null)
        {
            _cachedGetAllSignalValuesDelegate = (runtime) =>
            {
                try
                {
                    var result = getAllSignalValuesMethod.Invoke(runtime, null);
                    return (Dictionary<string, double>)result;
                }
                catch
                {
                    return new Dictionary<string, double>();
                }
            };
        }
    }

    #endregion

    #region ToString重写

    /// <inheritdoc/>
    public override string? ToString()
    {
        var canType = ChannelOptions?.CanType.ToString() ?? "Unknown";
        var canBaudRate = ChannelOptions?.CanBaudRate ?? 0;
        return $"CAN[{canType}@{canBaudRate}]";
    }

    #endregion

        /// <summary>
    /// 外部触发发送CAN报文
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">数据</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> TriggerSendCanMessageAsync(long deviceId, uint canId, byte[] data)
    {
        try
        {
            // 直接发送CAN帧
            var result = await SendCanFrameAsync(canId, data);
            return result ? OperResult.Success : new OperResult("发送CAN帧失败");
        }
        catch (Exception ex)
        {
            Logger?.Error($"触发发送CAN报文失败: DeviceId={deviceId}, CanId=0x{canId:X8}, 错误={ex.Message}");
            return new OperResult(ex);
        }
    }
}

/// <summary>
/// CAN消息包装器，用于将CAN帧适配到通道消息系统
/// </summary>
public class CanMessage : MessageBase, IRequestInfo
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frame">CAN帧</param>
    public CanMessage(CanFrame frame)
    {
        Frame = frame;
        Content = frame.Data;
        Sign = (int)frame.Id; // 使用CAN ID作为消息标识
    }

    /// <summary>
    /// CAN帧
    /// </summary>
    public CanFrame Frame { get; }

    /// <summary>
    /// CAN ID
    /// </summary>
    public uint CanId => Frame.Id;

    /// <summary>
    /// 是否为扩展帧
    /// </summary>
    public bool IsExtended => Frame.IsExtended;

    /// <summary>
    /// 是否为远程帧
    /// </summary>
    public bool IsRemote => Frame.IsRemote;

    /// <summary>
    /// 是否为CAN FD帧
    /// </summary>
    public bool IsCanFd => Frame.IsCanFd;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTimeOffset Timestamp => Frame.Timestamp;

    /// <inheritdoc/>
    public override string ToString()
    {
        return $"CAN Message - ID: 0x{CanId:X}, Data: {Frame.Data?.ToHexString(' ') ?? ""}";
    }
}

namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanMessage种子数据
/// </summary>
public class BatCanMessageSeedData : ISqlSugarEntitySeedData<CanMessage>
{
    /// <inheritdoc/>
    public IEnumerable<CanMessage> SeedData()
    {
        var messages = new List<CanMessage>();

        // 56V电池包协议消息
        // 0x189022F4 - 总电压、总电流、SOC
        messages.Add(new CanMessage
        {
            Id = 110001,
            CanBmsId = 110001, // Bat CanBms ID
            MessageId = 0x189022F4,
            DisplayId = "0x189022F4",
            MessageName = "Bat_Basic_Info",
            DLC = 8,
            IsExtended = true,
            IsRemote = false,
            IsCanFd = false,
            MessageType = CanMessageTypeEnum.Receive,
            SendStartTime = null,
            SendCount = 0,
            SendInterval = 0,
            SendDataType = CanSendDataTypeEnum.ByteData,
            SendData = null,
            SendSignalConfig = null,
            Description = "56V电池包基本信息（总电压、总电流、SOC）",
            Enable = true
        });

        // 0x189122F4 - 单体电压信息
        messages.Add(new CanMessage
        {
            Id = 110002,
            CanBmsId = 110001, // Bat CanBms ID
            MessageId = 0x189122F4,
            DisplayId = "0x189122F4",
            MessageName = "Bat_Cell_Voltage_Info",
            DLC = 8,
            IsExtended = true,
            IsRemote = false,
            IsCanFd = false,
            MessageType = CanMessageTypeEnum.Receive,
            SendStartTime = null,
            SendCount = 0,
            SendInterval = 0,
            SendDataType = CanSendDataTypeEnum.ByteData,
            SendData = null,
            SendSignalConfig = null,
            Description = "56V电池包单体电压信息（最高/最低单体电压及cell号）",
            Enable = true
        });

        // 0x189222F4 - 单体温度信息
        messages.Add(new CanMessage
        {
            Id = 110003,
            CanBmsId = 110001, // Bat CanBms ID
            MessageId = 0x189222F4,
            DisplayId = "0x189222F4",
            MessageName = "Bat_Cell_Temperature_Info",
            DLC = 8,
            IsExtended = true,
            IsRemote = false,
            IsCanFd = false,
            MessageType = CanMessageTypeEnum.Receive,
            SendStartTime = null,
            SendCount = 0,
            SendInterval = 0,
            SendDataType = CanSendDataTypeEnum.ByteData,
            SendData = null,
            SendSignalConfig = null,
            Description = "56V电池包单体温度信息（最高/最低单体温度及cell号）",
            Enable = true
        });

        return messages;
    }
} 
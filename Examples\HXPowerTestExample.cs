using Matrix.Foundation;
using Matrix.Foundation.Channel;
using Matrix.PowerPlugin.HXPower;
using System.Text;

namespace Examples;

/// <summary>
/// HXPower设备测试示例
/// </summary>
public class HXPowerTestExample
{
    /// <summary>
    /// 测试HXPower设备功能
    /// </summary>
    public static async Task RunTestAsync()
    {
        Console.WriteLine("=== HXPower设备测试示例 ===");
        
        // 创建HXPower设备实例
        var hxPowerDevice = new HXPowerCanDevice();
        
        // 模拟CAN通道（实际使用时需要真实的CAN通道）
        var canChannel = new TestCanChannel();
        
        try
        {
            // 初始化设备
            hxPowerDevice.InitChannel(canChannel);
            Console.WriteLine("✓ HXPower设备初始化成功");
            
            // 测试1：设置所有通道为待机模式
            Console.WriteLine("\n--- 测试1：设置所有通道为待机模式 ---");
            var result = await hxPowerDevice.SetAllChannelsToStandbyAsync();
            Console.WriteLine($"设置待机模式结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 测试2：设置单个通道为恒流模式
            Console.WriteLine("\n--- 测试2：设置模块1通道1为恒流模式 ---");
            result = await hxPowerDevice.SetConstantCurrentAsync(1, 1, 5.0); // 5A
            Console.WriteLine($"设置恒流模式结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 测试3：设置单个通道为恒压模式
            Console.WriteLine("\n--- 测试3：设置模块1通道2为恒压模式 ---");
            result = await hxPowerDevice.SetConstantVoltageAsync(1, 2, 12.0); // 12V
            Console.WriteLine($"设置恒压模式结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 测试4：设置单个通道为恒功率模式
            Console.WriteLine("\n--- 测试4：设置模块2通道1为恒功率模式 ---");
            result = await hxPowerDevice.SetConstantPowerAsync(2, 1, 100.0); // 100W
            Console.WriteLine($"设置恒功率模式结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 测试5：批量发送控制命令
            Console.WriteLine("\n--- 测试5：批量发送控制命令 ---");
            var batchCommands = new[]
            {
                (1, 1, HXPowerDevice.HXPowerWorkMode.ConstantCurrent, 2.0),
                (1, 2, HXPowerDevice.HXPowerWorkMode.ConstantVoltage, 8.0),
                (2, 1, HXPowerDevice.HXPowerWorkMode.ConstantPower, 50.0),
                (2, 2, HXPowerDevice.HXPowerWorkMode.ConstantResistance, 10.0)
            };
            
            result = await hxPowerDevice.SendBatchControlCommandsAsync(batchCommands);
            Console.WriteLine($"批量发送命令结果: {(result.IsSuccess ? "成功" : "失败")} - {result.ErrorMessage}");
            
            // 测试6：模拟接收数据并获取设备状态
            Console.WriteLine("\n--- 测试6：模拟接收数据并获取设备状态 ---");
            
            // 模拟接收模块1通道1的数据报文
            var testData = GenerateTestDataFrame(HXPowerDevice.HXPowerWorkMode.ConstantCurrent, 8.5, 2.1);
            canChannel.SimulateDataReceived(0x0810FF41, testData);
            
            // 模拟接收心跳数据
            var heartbeatData = new byte[] { 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
            canChannel.SimulateDataReceived(0x1020FF02, heartbeatData);
            
            // 等待数据处理
            await Task.Delay(100);
            
            // 获取设备状态
            var deviceStatus = hxPowerDevice.GetDeviceStatus(1, 1);
            if (deviceStatus != null)
            {
                Console.WriteLine($"模块1通道1状态:");
                Console.WriteLine($"  工作模式: {deviceStatus.DeviceStatus}");
                Console.WriteLine($"  电压: {deviceStatus.Voltage:F5}V");
                Console.WriteLine($"  电流: {deviceStatus.Current:F5}A");
                Console.WriteLine($"  更新时间: {deviceStatus.LastUpdateTime}");
            }
            
            // 测试7：获取设备摘要
            Console.WriteLine("\n--- 测试7：获取设备摘要 ---");
            var summary = hxPowerDevice.GetDeviceSummary();
            Console.WriteLine($"设备摘要:");
            Console.WriteLine($"  总模块数: {summary.TotalModules}");
            Console.WriteLine($"  每模块通道数: {summary.ChannelsPerModule}");
            Console.WriteLine($"  总通道数: {summary.TotalChannels}");
            Console.WriteLine($"  在线通道数: {summary.OnlineCount}");
            Console.WriteLine($"  最后更新时间: {summary.LastUpdateTime}");
            
            // 测试8：获取在线状态
            Console.WriteLine("\n--- 测试8：获取在线状态 ---");
            var onlineStatus = hxPowerDevice.GetOnlineStatus();
            foreach (var kvp in onlineStatus.Take(5)) // 只显示前5个
            {
                Console.WriteLine($"  {kvp.Key}: {(kvp.Value ? "在线" : "离线")}");
            }
            
            Console.WriteLine("\n=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试异常: {ex.Message}");
        }
        finally
        {
            // 清理资源
            hxPowerDevice?.Dispose();
        }
    }
    
    /// <summary>
    /// 生成测试数据帧
    /// </summary>
    private static byte[] GenerateTestDataFrame(HXPowerDevice.HXPowerWorkMode workMode, double voltage, double current)
    {
        var data = new byte[8];
        
        // 第1字节：设备状态
        data[0] = (byte)workMode;
        
        // 第2-4字节：电压（分辨率0.00001V）
        var voltageRaw = (uint)(voltage / 0.00001);
        var voltageBytes = BitConverter.GetBytes(voltageRaw);
        Array.Copy(voltageBytes, 0, data, 1, 3);
        
        // 第5-8字节：电流（分辨率0.00001A，有符号）
        var currentRaw = (int)(current / 0.00001);
        var currentBytes = BitConverter.GetBytes(currentRaw);
        Array.Copy(currentBytes, 0, data, 4, 4);
        
        return data;
    }
    
    /// <summary>
    /// 解析CAN控制命令
    /// </summary>
    public static void ParseControlCommand(uint canId, byte[] data)
    {
        if (data.Length < 8) return;
        
        // 计算模块和通道号
        var channelOffset = canId - 0x04904102;
        var moduleNumber = (int)(channelOffset / 2) + 1;
        var channelNumber = (int)(channelOffset % 2) + 1;
        
        var workMode = (HXPowerDevice.HXPowerWorkMode)data[2];
        var controlValueRaw = BitConverter.ToInt32(data, 4);
        var controlValue = controlValueRaw * 0.00001;
        
        Console.WriteLine($"解析控制命令:");
        Console.WriteLine($"  CAN ID: 0x{canId:X8}");
        Console.WriteLine($"  模块: {moduleNumber}, 通道: {channelNumber}");
        Console.WriteLine($"  工作模式: {workMode}");
        Console.WriteLine($"  控制值: {controlValue:F5}");
        Console.WriteLine($"  原始数据: {BitConverter.ToString(data)}");
    }
}

/// <summary>
/// 测试用CAN通道
/// </summary>
public class TestCanChannel : CanChannel
{
    public event Action<uint, byte[]>? DataReceived;
    
    public TestCanChannel() : base(new CanChannelOptions { DeviceId = "test" })
    {
    }
    
    public override async ValueTask<OperResult> SendCanFrameAsync(uint canId, byte[] data, CancellationToken cancellationToken = default)
    {
        // 模拟发送成功
        Console.WriteLine($"发送CAN帧: ID=0x{canId:X8}, Data={BitConverter.ToString(data)}");
        
        // 解析控制命令
        if (canId >= 0x04904102 && canId <= 0x04904115)
        {
            HXPowerTestExample.ParseControlCommand(canId, data);
        }
        
        return new OperResult();
    }
    
    /// <summary>
    /// 模拟数据接收
    /// </summary>
    public void SimulateDataReceived(uint canId, byte[] data)
    {
        DataReceived?.Invoke(canId, data);
        
        // 触发已注册的处理器
        foreach (var processor in _frameProcessors)
        {
            processor(canId, data);
        }
    }
    
    private readonly List<Action<uint, byte[]>> _frameProcessors = new();
    
    public void RegisterCanFrameProcessor(Action<uint, byte[]> processor)
    {
        _frameProcessors.Add(processor);
    }
    
    public override async ValueTask<OperResult<bool>> ConnectAsync(CancellationToken cancellationToken = default)
    {
        return new OperResult<bool>(true);
    }
    
    public override async ValueTask<OperResult<bool>> DisconnectAsync(CancellationToken cancellationToken = default)
    {
        return new OperResult<bool>(true);
    }
    
    public override bool Online => true;
} 
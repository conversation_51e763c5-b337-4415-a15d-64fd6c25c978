namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanMessage种子数据
/// </summary>
public class HXPowerCanMessageSeedData : ISqlSugarEntitySeedData<CanMessage>
{
    /// <inheritdoc/>
    public IEnumerable<CanMessage> SeedData()
    {
        var messages = new List<CanMessage>();

        // 为10个模块，每个模块2个通道创建数据报文
        for (int module = 1; module <= 10; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                // 数据报文（接收）
                var dataCanId = 0x0810FF41 + (module - 1) * 2 + (channel - 1);
                messages.Add(new CanMessage
                {
                    Id = 20000 + (module - 1) * 4 + (channel - 1) * 2 + 1, // 数据报文ID
                    CanBmsId = 10001, // HXPower CanBms ID
                    MessageId = (uint)dataCanId,
                    DisplayId = $"0x{dataCanId:X8}",
                    MessageName = $"HXPower_Module{module}_Channel{channel}_Data",
                    DLC = 8,
                    IsExtended = true,
                    IsRemote = false,
                    IsCanFd = false,
                    MessageType = CanMessageTypeEnum.Receive,
                    SendStartTime = null,
                    SendCount = 0,
                    SendInterval = 0,
                    SendDataType = CanSendDataTypeEnum.ByteData,
                    SendData = null,
                    SendSignalConfig = null,
                    Description = $"模块{module}通道{channel}数据报文",
                    Enable = true
                });

                // 控制报文（发送）
                var controlCanId = 0x04904102 + (module - 1) * 2 + (channel - 1);
                messages.Add(new CanMessage
                {
                    Id = 20000 + (module - 1) * 4 + (channel - 1) * 2 + 2, // 控制报文ID
                    CanBmsId = 10001, // HXPower CanBms ID
                    MessageId = (uint)controlCanId,
                    DisplayId = $"0x{controlCanId:X8}",
                    MessageName = $"HXPower_Module{module}_Channel{channel}_Control",
                    DLC = 8,
                    IsExtended = true,
                    IsRemote = false,
                    IsCanFd = false,
                    MessageType = CanMessageTypeEnum.Send,
                    SendStartTime = null,
                    SendCount = 1,
                    SendInterval = 0,
                    SendDataType = CanSendDataTypeEnum.SignalValue,
                    SendData = null,
                    SendSignalConfig = "WorkMode=1,ControlValue=0", // 默认控制信号配置
                    Description = $"模块{module}通道{channel}控制报文",
                    Enable = true
                });
            }
        }

        // 心跳报文（接收）
        messages.Add(new CanMessage
        {
            Id = 30001,
            CanBmsId = 10001,
            MessageId = 0x1020FF02,
            DisplayId = "0x1020FF02",
            MessageName = "HXPower_Heartbeat",
            DLC = 1,
            IsExtended = true,
            IsRemote = false,
            IsCanFd = false,
            MessageType = CanMessageTypeEnum.Receive,
            SendStartTime = null,
            SendCount = 0,
            SendInterval = 0,
            SendDataType = CanSendDataTypeEnum.ByteData,
            SendData = null,
            SendSignalConfig = null,
            Description = "HXPower心跳报文",
            Enable = true
        });

        // 故障报文（接收）
        for (int module = 1; module <= 10; module++)
        {
            for (int channel = 1; channel <= 2; channel++)
            {
                var faultCanId = 0x1040ff41 + (module - 1) * 2 + (channel - 1);
                messages.Add(new CanMessage
                {
                    Id = 31000 + (module - 1) * 2 + (channel - 1),
                    CanBmsId = 10001,
                    MessageId = (uint)faultCanId,
                    DisplayId = $"0x{faultCanId:X8}",
                    MessageName = $"HXPower_Module{module}_Channel{channel}_Fault",
                    DLC = 8,
                    IsExtended = true,
                    IsRemote = false,
                    IsCanFd = false,
                    MessageType = CanMessageTypeEnum.Receive,
                    SendStartTime = null,
                    SendCount = 0,
                    SendInterval = 0,
                    SendDataType = CanSendDataTypeEnum.ByteData,
                    SendData = null,
                    SendSignalConfig = null,
                    Description = $"模块{module}通道{channel}故障报文",
                    Enable = true
                });
            }
        }

        return messages;
    }
} 
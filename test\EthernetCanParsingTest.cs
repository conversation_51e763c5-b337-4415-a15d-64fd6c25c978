using System;
using Matrix.Foundation;
using Xunit;

namespace Matrix.Tests
{
    /// <summary>
    /// 以太网CAN解析测试
    /// </summary>
    public class EthernetCanParsingTest
    {
        [Fact]
        public void TestParseEthernetCanPacket_SingleFrame()
        {
            // 测试数据：88 18 FF 22 F4 30 75 F4 7E 00 00 00 00
            var testData = new byte[] { 0x88, 0x18, 0xFF, 0x22, 0xF4, 0x30, 0x75, 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00 };
            
            var frame = CanUtility.ParseEthernetCanPacket(testData);
            
            // 验证CAN ID：22 F4 30 75 = 0x22F43075
            Assert.Equal(0x22F43075u, frame.Id);
            
            // 验证数据：F4 7E 00 00 00 00 (6字节)
            Assert.Equal(6, frame.Data.Length);
            Assert.Equal(0xF4, frame.Data[0]);
            Assert.Equal(0x7E, frame.Data[1]);
            Assert.Equal(0x00, frame.Data[2]);
            Assert.Equal(0x00, frame.Data[3]);
            Assert.Equal(0x00, frame.Data[4]);
            Assert.Equal(0x00, frame.Data[5]);
        }

        [Fact]
        public void TestParseEthernetCanPacket_SecondFrame()
        {
            // 测试数据：88 18 90 22 F4 E8 01 00 00 30 75 00 00
            var testData = new byte[] { 0x88, 0x18, 0x90, 0x22, 0xF4, 0xE8, 0x01, 0x00, 0x00, 0x30, 0x75, 0x00, 0x00 };
            
            var frame = CanUtility.ParseEthernetCanPacket(testData);
            
            // 验证CAN ID：22 F4 E8 01 = 0x22F4E801
            Assert.Equal(0x22F4E801u, frame.Id);
            
            // 验证数据：00 00 30 75 00 00 (6字节)
            Assert.Equal(6, frame.Data.Length);
            Assert.Equal(0x00, frame.Data[0]);
            Assert.Equal(0x00, frame.Data[1]);
            Assert.Equal(0x30, frame.Data[2]);
            Assert.Equal(0x75, frame.Data[3]);
            Assert.Equal(0x00, frame.Data[4]);
            Assert.Equal(0x00, frame.Data[5]);
        }

        [Fact]
        public void TestBuildEthernetCanPacket()
        {
            // 创建测试CAN帧
            var canId = 0x22F43075u;
            var canData = new byte[] { 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00 };
            var frame = new CanFrame(canId, canData);
            
            var packet = CanUtility.BuildEthernetCanPacket(frame);
            
            // 验证包格式：88 18 FF + CAN_ID + 数据
            Assert.Equal(13, packet.Length); // 7字节头部 + 6字节数据
            Assert.Equal(0x88, packet[0]);
            Assert.Equal(0x18, packet[1]);
            Assert.Equal(0xFF, packet[2]); // 帧类型
            
            // 验证CAN ID
            Assert.Equal(0x22, packet[3]);
            Assert.Equal(0xF4, packet[4]);
            Assert.Equal(0x30, packet[5]);
            Assert.Equal(0x75, packet[6]);
            
            // 验证数据
            Assert.Equal(0xF4, packet[7]);
            Assert.Equal(0x7E, packet[8]);
            Assert.Equal(0x00, packet[9]);
            Assert.Equal(0x00, packet[10]);
            Assert.Equal(0x00, packet[11]);
            Assert.Equal(0x00, packet[12]);
        }

        [Fact]
        public void TestParseEthernetCanPacket_InvalidHeader()
        {
            // 测试无效帧头
            var testData = new byte[] { 0x99, 0x18, 0xFF, 0x22, 0xF4, 0x30, 0x75 };
            
            Assert.Throws<ArgumentException>(() => CanUtility.ParseEthernetCanPacket(testData));
        }

        [Fact]
        public void TestParseEthernetCanPacket_TooShort()
        {
            // 测试数据太短
            var testData = new byte[] { 0x88, 0x18, 0xFF };
            
            Assert.Throws<ArgumentException>(() => CanUtility.ParseEthernetCanPacket(testData));
        }

        [Fact]
        public void TestParseMultipleFrames()
        {
            // 模拟多帧数据流
            var multiFrameData = new byte[]
            {
                // 第一帧：88 18 FF 22 F4 30 75 F4 7E 00 00 00 00
                0x88, 0x18, 0xFF, 0x22, 0xF4, 0x30, 0x75, 0xF4, 0x7E, 0x00, 0x00, 0x00, 0x00,
                // 第二帧：88 18 90 22 F4 E8 01 00 00 30 75 00 00
                0x88, 0x18, 0x90, 0x22, 0xF4, 0xE8, 0x01, 0x00, 0x00, 0x30, 0x75, 0x00, 0x00
            };

            // 解析第一帧
            var frame1Data = new byte[13];
            Array.Copy(multiFrameData, 0, frame1Data, 0, 13);
            var frame1 = CanUtility.ParseEthernetCanPacket(frame1Data);
            
            Assert.Equal(0x22F43075u, frame1.Id);
            Assert.Equal(6, frame1.Data.Length);

            // 解析第二帧
            var frame2Data = new byte[13];
            Array.Copy(multiFrameData, 13, frame2Data, 0, 13);
            var frame2 = CanUtility.ParseEthernetCanPacket(frame2Data);
            
            Assert.Equal(0x22F4E801u, frame2.Id);
            Assert.Equal(6, frame2.Data.Length);
        }
    }
}

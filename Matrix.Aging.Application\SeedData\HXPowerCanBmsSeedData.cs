namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanBms种子数据
/// </summary>
public class HXPowerCanBmsSeedData : ISqlSugarEntitySeedData<CanBms>
{
    /// <inheritdoc/>
    public IEnumerable<CanBms> SeedData()
    {
        return new[]
        {
            new CanBms
            {
                Id = 10001,
                BmsName = "HXPower",
                Description = "HXPower电源协议配置",
                Enable = true
            }
        };
    }
} 
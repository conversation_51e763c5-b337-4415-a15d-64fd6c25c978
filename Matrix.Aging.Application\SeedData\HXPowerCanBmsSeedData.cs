namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanBms种子数据
/// </summary>
public class HXPowerCanBmsSeedData : ISqlSugarEntitySeedData<CanBms>
{
    /// <inheritdoc/>
    public IEnumerable<CanBms> SeedData()
    {
        return new[]
        {
            new CanBms
            {
                Id = 10001,
                BmsName = "HXPower",
                Description = "HXPower电源协议配置",
                DeviceId = 0, // 协议文件不绑定特定设备，使用0作为默认值
                Enable = true
            }
        };
    }
} 
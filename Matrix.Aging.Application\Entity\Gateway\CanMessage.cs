using BootstrapBlazor.Components;
using Matrix.Foundation;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN报文表
/// </summary>
[SugarTable("can_message", TableDescription = "CAN报文表")]
[Tenant(SqlSugarConst.DB_Custom)]
[SugarIndex("index_can_bms", nameof(CanMessage.CanBmsId), OrderByType.Asc)]
[SugarIndex("unique_message_id", nameof(CanMessage.MessageId), OrderByType.Asc, nameof(CanMessage.CanBmsId), OrderByType.Asc, true)]
public class CanMessage : BaseDataEntity, IValidatableObject
{
    /// <summary>
    /// 所属CanBms ID
    /// </summary>
    [SugarColumn(ColumnDescription = "CanBms ID")]
    [AutoGenerateColumn(Visible = true, Order = 1, Filterable = false, Sortable = false)]
    [Required]
    [NotNull]
    public virtual long CanBmsId { get; set; }

    /// <summary>
    /// 报文ID（十进制）
    /// </summary>
    [SugarColumn(ColumnDescription = "报文ID")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 1)]
    [Required]
    public virtual uint MessageId { get; set; }

    /// <summary>
    /// 报文显示ID（十六进制显示，如0x0810FF41）
    /// </summary>
    [SugarColumn(ColumnDescription = "报文显示ID", Length = 20, IsNullable = false)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 2)]
    [Required]
    public virtual string DisplayId { get; set; }

    /// <summary>
    /// 报文名称
    /// </summary>
    [SugarColumn(ColumnDescription = "报文名称", Length = 100, IsNullable = false)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 3)]
    [Required]
    public virtual string MessageName { get; set; }

    /// <summary>
    /// 数据长度代码（DLC）
    /// </summary>
    [SugarColumn(ColumnDescription = "数据长度")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual byte DLC { get; set; } = 8;

    /// <summary>
    /// 是否扩展帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否扩展帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsExtended { get; set; } = false;

    /// <summary>
    /// 是否远程帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否远程帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsRemote { get; set; } = false;

    /// <summary>
    /// 是否CAN FD帧
    /// </summary>
    [SugarColumn(ColumnDescription = "是否CAN FD帧")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsCanFd { get; set; } = false;

    /// <summary>
    /// 报文类型（接收/发送）
    /// </summary>
    [SugarColumn(ColumnDescription = "报文类型")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual CanMessageTypeEnum MessageType { get; set; } = CanMessageTypeEnum.Receive;

    /// <summary>
    /// 开始发送时间（发送报文时使用）
    /// </summary>
    [SugarColumn(ColumnDescription = "开始发送时间", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual DateTime? SendStartTime { get; set; }

    /// <summary>
    /// 发送次数（0为循环发送，大于0则按设置值的次数发送）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送次数")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int SendCount { get; set; } = 0;

    /// <summary>
    /// 发送间隔时间（毫秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送间隔时间")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual int SendInterval { get; set; } = 1000;

    /// <summary>
    /// 发送数据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "发送数据类型")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual CanSendDataTypeEnum SendDataType { get; set; } = CanSendDataTypeEnum.ByteData;

    /// <summary>
    /// 发送数据（字节数据格式：01 02 03 04 05 06 07 08）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送数据", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? SendData { get; set; }

    /// <summary>
    /// 发送信号配置（信号值格式：Signal1=Life,Signal2=1,Signal3=2）
    /// </summary>
    [SugarColumn(ColumnDescription = "发送信号配置", Length = 500, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? SendSignalConfig { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? Description { get; set; }

    /// <summary>
    /// 使能
    /// </summary>
    [SugarColumn(ColumnDescription = "使能")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool Enable { get; set; } = true;



    /// <summary>
    /// 验证方法
    /// </summary>
    /// <param name="validationContext">验证上下文</param>
    /// <returns>验证结果</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // 验证报文ID范围
        if (IsExtended)
        {
            if (MessageId > 0x1FFFFFFF) // 29位扩展帧
            {
                results.Add(new ValidationResult("扩展帧ID不能超过0x1FFFFFFF", new[] { nameof(MessageId) }));
            }
        }
        else
        {
            if (MessageId > 0x7FF) // 11位标准帧
            {
                results.Add(new ValidationResult("标准帧ID不能超过0x7FF", new[] { nameof(MessageId) }));
            }
        }

        // 验证DLC范围
        if (IsCanFd)
        {
            if (DLC > 64)
            {
                results.Add(new ValidationResult("CAN FD帧DLC不能超过64", new[] { nameof(DLC) }));
            }
        }
        else
        {
            if (DLC > 8)
            {
                results.Add(new ValidationResult("标准CAN帧DLC不能超过8", new[] { nameof(DLC) }));
            }
        }

        // 验证发送数据格式
        if (MessageType == CanMessageTypeEnum.Send && SendDataType == CanSendDataTypeEnum.ByteData)
        {
            if (!string.IsNullOrWhiteSpace(SendData))
            {
                var bytes = SendData.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (bytes.Length > DLC)
                {
                    results.Add(new ValidationResult($"发送数据字节数不能超过DLC({DLC})", new[] { nameof(SendData) }));
                }

                foreach (var byteStr in bytes)
                {
                    if (!byte.TryParse(byteStr, System.Globalization.NumberStyles.HexNumber, null, out _))
                    {
                        results.Add(new ValidationResult($"发送数据格式错误，应为十六进制字节：{byteStr}", new[] { nameof(SendData) }));
                        break;
                    }
                }
            }
        }

        return results;
    }
}

/// <summary>
/// CAN报文类型枚举
/// </summary>
public enum CanMessageTypeEnum
{
    /// <summary>
    /// 接收报文
    /// </summary>
    Receive = 0,

    /// <summary>
    /// 发送报文
    /// </summary>
    Send = 1
}

/// <summary>
/// CAN发送数据类型枚举
/// </summary>
public enum CanSendDataTypeEnum
{
    /// <summary>
    /// 字节数据（如：01 02 03 04 05 06 07 08）
    /// </summary>
    ByteData = 0,

    /// <summary>
    /// 信号值（如：Signal1=Life,Signal2=1,Signal3=2）
    /// </summary>
    SignalValue = 1
}

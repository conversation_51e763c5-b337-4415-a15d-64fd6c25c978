﻿
using System.ComponentModel.DataAnnotations;
using System.IO.Ports;

namespace Matrix.Foundation
{
    public abstract class ChannelOptionsBase : IValidatableObject
    {
        /// <summary>
        /// 通道类型
        /// </summary>
        public virtual ChannelTypeEnum ChannelType { get; set; }

        #region 以太网

        /// <summary>
        /// 远程ip
        /// </summary>
        [UriValidation]
        public virtual string RemoteUrl { get; set; } = "127.0.0.1:502";

        /// <summary>
        /// 本地绑定ip，分号分隔，例如：***********:502;***********:502，表示绑定***********:502和***********:502
        /// </summary>
        [UriValidation]
        public virtual string BindUrl { get; set; }

        #endregion

        #region 串口

        /// <summary>
        /// COM
        /// </summary>
        public virtual string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        public virtual int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        public virtual int DataBits { get; set; } = 8;

        /// <summary>
        /// 校验位
        /// </summary>
        public virtual Parity Parity { get; set; } = System.IO.Ports.Parity.None;

        /// <summary>
        /// 停止位
        /// </summary>
        public virtual StopBits StopBits { get; set; } = System.IO.Ports.StopBits.One;

        /// <summary>
        /// DtrEnable
        /// </summary>
        public virtual bool DtrEnable { get; set; } = true;

        /// <summary>
        /// RtsEnable
        /// </summary>
        public virtual bool RtsEnable { get; set; } = true;

        /// <inheritdoc/>
        [MinValue(1)]
        public virtual int MaxConcurrentCount { get; set; } = 1;

        /// <inheritdoc/>
        [MinValue(100)]
        public virtual int CacheTimeout { get; set; } = 500;
        /// <inheritdoc/>
        [MinValue(100)]
        public virtual ushort ConnectTimeout { get; set; } = 3000;

        #endregion

        #region CAN
        /// <summary>
        /// CAN类型
        /// 根据系统，有不同启动方式:SocketCAN,EthernetCAN,ZlgCAN
        /// </summary>
        public virtual CanBusType CanType { get; set; } = CanBusType.SocketCan;

        /// <summary>
        /// 是否启用CAN FD协议
        /// </summary>
        public virtual bool IsCanFd { get; set; } = false;

        /// <summary>
        /// CAN总线速度
        /// </summary>
        public virtual CanBusSpeed CanBaudRate { get; set; } = CanBusSpeed.Speed250K;

        /// <summary>
        /// CAN设置 - 根据CanType有不同格式：
        /// SocketCAN: can0, can1, vcan0...
        /// EthernetCAN: GC212,***********0:23 (型号,IP:端口)
        /// ZlgCAN:
        ///   USBCAN: USBCAN2,0,0 或 USBCANFD-200U,0,0
        ///   CANET: CANETTCP,***********78:4001 或 CANFDNET-400U-TCP,***********78:4001
        ///   PCICAN: PCI9840,0,0 或 PCIECANFD-400U,0,0
        /// </summary>
        public virtual string CanSet { get; set; } = "can0";

        /// <summary>
        /// CAN BMS ID（新架构 - 通道直接选择协议文件）
        /// </summary>
        public virtual long? CanBmsId { get; set; }
        #endregion

        public virtual int MaxClientCount { get; set; }
        public virtual int CheckClearTime { get; set; }
        public virtual string Heartbeat { get; set; }

        #region dtu终端
        public virtual int HeartbeatTime { get; set; }
        public virtual string DtuId { get; set; }
        #endregion
        public virtual DtuSeviceType DtuSeviceType { get; set; }

        /// <summary>
        /// 是否用于老化测试
        /// </summary>
        public virtual bool IsAging { get; set; } = false;

        /// <summary>
        /// 验证CAN配置的有效性
        /// </summary>
        /// <returns>是否有效</returns>
        public virtual bool IsCanConfigValid()
        {
            if (ChannelType != ChannelTypeEnum.CAN)
                return true;

            return CanConfigParser.IsValidCanConfig(CanType, CanSet, CanBaudRate);
        }

        /// <summary>
        /// 获取解析后的CAN配置信息
        /// </summary>
        /// <returns>CAN配置信息</returns>
        public virtual CanConfigInfo? GetCanConfigInfo()
        {
            if (ChannelType != ChannelTypeEnum.CAN)
                return null;

            try
            {
                return CanConfigParser.ParseCanConfig(CanType, CanSet);
            }
            catch
            {
                return null;
            }
        }

        public virtual IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (ChannelType == ChannelTypeEnum.TcpClient)
            {
                if (string.IsNullOrEmpty(RemoteUrl))
                {
                    yield return new ValidationResult(DefaultResource.Localizer["RemoteUrlNotNull"], new[] { nameof(RemoteUrl) });
                }
            }
            else if (ChannelType == ChannelTypeEnum.TcpService)
            {
                if (string.IsNullOrEmpty(BindUrl))
                {
                    yield return new ValidationResult(DefaultResource.Localizer["BindUrlNotNull"], new[] { nameof(BindUrl) });
                }
            }
            else if (ChannelType == ChannelTypeEnum.UdpSession)
            {
                if (string.IsNullOrEmpty(BindUrl) && string.IsNullOrEmpty(RemoteUrl))
                {
                    yield return new ValidationResult(DefaultResource.Localizer["BindUrlOrRemoteUrlNotNull"], new[] { nameof(BindUrl), nameof(RemoteUrl) });
                }
            }
            else if (ChannelType == ChannelTypeEnum.SerialPort)
            {
                if (string.IsNullOrEmpty(PortName))
                {
                    yield return new ValidationResult(DefaultResource.Localizer["PortNameNotNull"], new[] { nameof(PortName) });
                }
            }
            else if (ChannelType == ChannelTypeEnum.CAN)
            {
                if (!IsCanConfigValid())
                {
                    yield return new ValidationResult("CAN配置无效，请检查CanType和CanSet的格式", new[] { nameof(CanType), nameof(CanSet) });
                }
            }
        }


        public override string ToString()
        {
            switch (ChannelType)
            {
                case ChannelTypeEnum.TcpClient:
                    return RemoteUrl;
                case ChannelTypeEnum.TcpService:
                    return BindUrl;
                case ChannelTypeEnum.SerialPort:
                    return PortName;
                case ChannelTypeEnum.UdpSession:
                    return BindUrl.IsNullOrEmpty() ? RemoteUrl : BindUrl;
                case ChannelTypeEnum.CAN:
                    return $"CAN[{CanType}@{CanBaudRate}]:{CanSet}";
                case ChannelTypeEnum.Other:
                    return string.Empty;
            }
            return string.Empty;
        }
    }
}
namespace Matrix.Aging.Application;

/// <summary>
/// HXPower CanBms种子数据
/// </summary>
public class BatCanBmsSeedData : ISqlSugarEntitySeedData<CanBms>
{
    /// <inheritdoc/>
    public IEnumerable<CanBms> SeedData()
    {
        return new[]
        {
            new CanBms
            {
                Id = 110001,
                BmsName = "Bat",
                Description = "56V电池协议",
                DeviceId = 0, // 协议文件不绑定特定设备，使用0作为默认值
                Enable = true
            }
        };
    }
} 
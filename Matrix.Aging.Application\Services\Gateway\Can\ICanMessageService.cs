namespace Matrix.Aging.Application;

/// <summary>
/// CAN BMS服务接口
/// </summary>
public interface ICanBmsService
{
    /// <summary>
    /// 根据ID获取CAN BMS
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>CAN BMS</returns>
    Task<CanBms?> GetCanBmsByIdAsync(long id);

    /// <summary>
    /// 根据ID获取CAN BMS（同步方法）
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>CAN BMS</returns>
    CanBms? GetCanBmsById(long id);

    /// <summary>
    /// 获取所有CAN BMS
    /// </summary>
    /// <returns>CAN BMS列表</returns>
    Task<List<CanBms>> GetAllCanBmsAsync();

    /// <summary>
    /// 根据名称获取CAN BMS
    /// </summary>
    /// <param name="bmsName">BMS名称</param>
    /// <returns>CAN BMS</returns>
    Task<CanBms?> GetCanBmsByNameAsync(string bmsName);

    /// <summary>
    /// 创建CAN BMS
    /// </summary>
    /// <param name="canBms">CAN BMS</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateCanBmsAsync(CanBms canBms);

    /// <summary>
    /// 更新CAN BMS
    /// </summary>
    /// <param name="canBms">CAN BMS</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCanBmsAsync(CanBms canBms);

    /// <summary>
    /// 删除CAN BMS
    /// </summary>
    /// <param name="id">BMS ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteCanBmsAsync(long id);

    /// <summary>
    /// 批量创建CAN BMS
    /// </summary>
    /// <param name="canBmsList">CAN BMS列表</param>
    /// <returns>创建结果</returns>
    Task<bool> BatchCreateCanBmsAsync(List<CanBms> canBmsList);

    /// <summary>
    /// 根据设备ID获取CAN BMS列表（向后兼容性方法）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>CAN BMS列表</returns>

}

/// <summary>
/// CAN报文服务接口
/// </summary>
public interface ICanMessageService
{
    /// <summary>
    /// 根据ID获取CAN报文
    /// </summary>
    /// <param name="id">报文ID</param>
    /// <returns>CAN报文</returns>
    Task<CanMessage?> GetCanMessageByIdAsync(long id);

    /// <summary>
    /// 根据CanBms ID获取所有CAN报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN报文列表</returns>
    Task<List<CanMessage>> GetCanMessagesByCanBmsIdAsync(long canBmsId);

    /// <summary>
    /// 根据报文ID获取CAN报文
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <param name="messageId">报文ID</param>
    /// <returns>CAN报文</returns>
    Task<CanMessage?> GetCanMessageByMessageIdAsync(long canBmsId, uint messageId);

    /// <summary>
    /// 创建CAN报文
    /// </summary>
    /// <param name="canMessage">CAN报文</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateCanMessageAsync(CanMessage canMessage);

    /// <summary>
    /// 更新CAN报文
    /// </summary>
    /// <param name="canMessage">CAN报文</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCanMessageAsync(CanMessage canMessage);

    /// <summary>
    /// 删除CAN报文
    /// </summary>
    /// <param name="id">报文ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteCanMessageAsync(long id);

    /// <summary>
    /// 批量创建CAN报文
    /// </summary>
    /// <param name="canMessages">CAN报文列表</param>
    /// <returns>创建结果</returns>
    Task<bool> BatchCreateCanMessagesAsync(List<CanMessage> canMessages);

    /// <summary>
    /// 获取启用发送的CAN报文列表
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN报文列表</returns>
    Task<List<CanMessage>> GetSendEnabledCanMessagesAsync(long canBmsId);
}

/// <summary>
/// CAN信号服务接口
/// </summary>
public interface ICanSignalService
{
    /// <summary>
    /// 根据ID获取CAN信号
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>CAN信号</returns>
    Task<CanSignal?> GetCanSignalByIdAsync(long id);

    /// <summary>
    /// 根据ID获取CAN信号（同步方法）
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>CAN信号</returns>
    CanSignal? GetCanSignalById(long id);

    /// <summary>
    /// 根据CanMessage ID获取所有CAN信号
    /// </summary>
    /// <param name="canMessageId">CanMessage ID</param>
    /// <returns>CAN信号列表</returns>
    Task<List<CanSignal>> GetCanSignalsByCanMessageIdAsync(long canMessageId);

    /// <summary>
    /// 根据CanBms ID获取所有CAN信号
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN信号列表</returns>
    Task<List<CanSignal>> GetCanSignalsByCanBmsIdAsync(long canBmsId);

    /// <summary>
    /// 根据信号名称获取CAN信号
    /// </summary>
    /// <param name="canMessageId">CanMessage ID</param>
    /// <param name="signalName">信号名称</param>
    /// <returns>CAN信号</returns>
    Task<CanSignal?> GetCanSignalByNameAsync(long canMessageId, string signalName);

    /// <summary>
    /// 创建CAN信号
    /// </summary>
    /// <param name="canSignal">CAN信号</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateCanSignalAsync(CanSignal canSignal);

    /// <summary>
    /// 更新CAN信号
    /// </summary>
    /// <param name="canSignal">CAN信号</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCanSignalAsync(CanSignal canSignal);

    /// <summary>
    /// 删除CAN信号
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteCanSignalAsync(long id);

    /// <summary>
    /// 批量创建CAN信号
    /// </summary>
    /// <param name="canSignals">CAN信号列表</param>
    /// <returns>创建结果</returns>
    Task<bool> BatchCreateCanSignalsAsync(List<CanSignal> canSignals);
}

/// <summary>
/// CAN变量服务接口
/// </summary>
public interface ICanVariableService
{
    /// <summary>
    /// 根据ID获取CAN变量
    /// </summary>
    /// <param name="id">变量ID</param>
    /// <returns>CAN变量</returns>
    Task<CanVariable?> GetCanVariableByIdAsync(long id);

    /// <summary>
    /// 根据设备ID获取所有CAN变量
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>CAN变量列表</returns>
    Task<List<CanVariable>> GetCanVariablesByDeviceIdAsync(long deviceId);

    /// <summary>
    /// 根据CAN信号ID获取CAN变量
    /// </summary>
    /// <param name="canSignalId">CAN信号ID</param>
    /// <returns>CAN变量</returns>
    Task<CanVariable?> GetCanVariableBySignalIdAsync(long canSignalId);

    /// <summary>
    /// 创建CAN变量
    /// </summary>
    /// <param name="canVariable">CAN变量</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateCanVariableAsync(CanVariable canVariable);

    /// <summary>
    /// 更新CAN变量
    /// </summary>
    /// <param name="canVariable">CAN变量</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCanVariableAsync(CanVariable canVariable);

    /// <summary>
    /// 删除CAN变量
    /// </summary>
    /// <param name="id">变量ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteCanVariableAsync(long id);

    /// <summary>
    /// 批量创建CAN变量
    /// </summary>
    /// <param name="canVariables">CAN变量列表</param>
    /// <returns>创建结果</returns>
    Task<bool> BatchCreateCanVariablesAsync(List<CanVariable> canVariables);
}

/// <summary>
/// CAN发送服务接口
/// </summary>
public interface ICanSendService
{
    /// <summary>
    /// 发送CAN报文
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="messageId">报文ID</param>
    /// <param name="data">数据</param>
    /// <returns>发送结果</returns>
    Task<bool> SendCanMessageAsync(long deviceId, uint messageId, byte[] data);

    /// <summary>
    /// 启动周期发送
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="canMessageId">CAN报文ID</param>
    /// <returns>启动结果</returns>
    Task<bool> StartPeriodicSendAsync(long deviceId, long canMessageId);

    /// <summary>
    /// 停止周期发送
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="canMessageId">CAN报文ID</param>
    /// <returns>停止结果</returns>
    Task<bool> StopPeriodicSendAsync(long deviceId, long canMessageId);

    /// <summary>
    /// 根据信号值构建CAN数据
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <param name="signalValues">信号值字典</param>
    /// <returns>CAN数据</returns>
    Task<byte[]> BuildCanDataFromSignalsAsync(long canBmsId, Dictionary<string, object> signalValues);

    /// <summary>
    /// 解析发送信号配置
    /// </summary>
    /// <param name="signalConfig">信号配置字符串（如：Signal1=Life,Signal2=1,Signal3=2）</param>
    /// <returns>信号值字典</returns>
    Dictionary<string, object> ParseSendSignalConfig(string signalConfig);
}

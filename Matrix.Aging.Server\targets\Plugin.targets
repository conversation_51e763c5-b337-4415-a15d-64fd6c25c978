<Project>
	<!--插件隔离-->

	<ItemGroup>
		<!--Modbus 插件-->
		<PackageReference Include="Matrix.PowerPlugin.Modbus" Version="$(PluginVersion)" GeneratePathProperty="true">
			<Private>false</Private>
			<IncludeAssets> native;</IncludeAssets>
		</PackageReference>
		<!--HXPower 插件-->
		<PackageReference Include="Matrix.PowerPlugin.HXPower" Version="$(PluginVersion)" GeneratePathProperty="true">
			<Private>false</Private>
			<IncludeAssets> native;</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<Target Name="CopyPluginNugetPackages" AfterTargets="Build">
		<PropertyGroup>
			<PluginFolder>$(TargetDir)Plugins\</PluginFolder>
			<MatrixPluginFolder>$(TargetDir)MatrixPlugins\</MatrixPluginFolder>
		</PropertyGroup>
		<RemoveDir Directories="$(PluginFolder)" />
		<RemoveDir Directories="$(MatrixPluginFolder)" />
		<PropertyGroup>
			<PluginTargetFramework>net8.0</PluginTargetFramework>
		</PropertyGroup>

		<ItemGroup>
			<!-- setting up the variable for convenience -->
			<PkgMatrix_PowerPlugin_ModbusPackageFiles Include="$(PkgMatrix_PowerPlugin_Modbus)\Content\$(PluginTargetFramework)\**\*.*" />
			<PkgMatrix_PowerPlugin_HXPowerPackageFiles Include="$(PkgMatrix_PowerPlugin_HXPower)\Content\$(PluginTargetFramework)\**\*.*" />
		</ItemGroup>

		<Message Text="将插件复制到插件目录 $(MatrixPluginFolder)" Importance="high" />
		<Copy SourceFiles="@(PkgMatrix_PowerPlugin_ModbusPackageFiles)" DestinationFolder="$(MatrixPluginFolder)Matrix.Plugin.Modbus%(RecursiveDir)" />
		<Copy SourceFiles="@(PkgMatrix_PowerPlugin_HXPowerPackageFiles)" DestinationFolder="$(MatrixPluginFolder)Matrix.Plugin.HXPower%(RecursiveDir)" />

	</Target>


</Project>

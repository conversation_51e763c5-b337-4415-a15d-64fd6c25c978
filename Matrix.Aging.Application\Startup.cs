﻿
using Mapster;

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

using System.Reflection;

namespace Matrix.Aging.Application;

[AppStartup(-100)]
public class Startup : AppStartup
{
    public void Configure(IServiceCollection services)
    {
        services.AddConfigurableOptions<ChannelThreadOptions>();
        services.AddConfigurableOptions<GatewayLogOptions>();
        services.AddConfigurableOptions<RpcLogOptions>();

        //底层多语言配置
        //Foundation.LocalizerUtil.SetLocalizerFactory((a) => App.CreateLocalizerByType(a));

        TypeAdapterConfig.GlobalSettings.Scan(App.Assemblies.ToArray());
        // 配置默认全局映射（支持覆盖）
        TypeAdapterConfig.GlobalSettings.Default
              .PreserveReference(true);

        //运行日志写入数据库配置
        services.AddDatabaseLogging<BackendLogDatabaseLoggingWriter>(options =>
        {
            options.WriteFilter = (logMsg) =>
            {
                return (
                !logMsg.LogName.StartsWith("System") &&
                !logMsg.LogName.StartsWith("Microsoft") &&
                !logMsg.LogName.StartsWith("Blazor") &&
                !logMsg.LogName.StartsWith("BootstrapBlazor")
                );
            };
        });

        services.AddSingleton<IChannelThreadManage, ChannelThreadManage>();
        services.AddSingleton<IChannelService, ChannelService>();
        services.AddSingleton<IChannelRuntimeService, ChannelRuntimeService>();
        services.AddSingleton<IVariableService, VariableService>();
        services.AddSingleton<IVariableRuntimeService, VariableRuntimeService>();
        services.AddSingleton<IDeviceService, DeviceService>();
        services.AddSingleton<IDeviceRuntimeService, DeviceRuntimeService>();
        services.AddSingleton<IPluginService, PluginService>();
        services.AddSingleton<IBackendLogService, BackendLogService>();
        services.AddSingleton<IRpcLogService, RpcLogService>();
        services.AddSingleton<IRpcService, RpcService>();
        services.AddScoped<IGatewayExportService, GatewayExportService>();

        services.AddGatewayHostedService<IAlarmHostedService, AlarmHostedService>();
        services.AddGatewayHostedService<IGatewayMonitorHostedService, GatewayMonitorHostedService>();

        // 注册老化测试相关服务
        services.AddSingleton<IAgingTestService, AgingTestService>();
        services.AddSingleton<IAgingDataService, AgingDataService>();
        services.AddHostedService<AgingInitializationService>();

        // 注册CAN相关服务
        services.AddSingleton<ICanBmsService, CanBmsService>();
        services.AddSingleton<ICanMessageService, CanMessageService>();
        services.AddSingleton<ICanSignalService, CanSignalService>();
        services.AddSingleton<ICanSendMessageService, CanSendMessageService>();
    }

    public void Use(IApplicationBuilder applicationBuilder)
    {
        var serviceProvider = applicationBuilder.ApplicationServices;
        //检查ConfigId
        var configIdGroup = DbContext.DbConfigs.GroupBy(it => it.ConfigId);
        foreach (var configId in configIdGroup)
        {
            if (configId.Count() > 1) throw new($"Sqlsugar connect configId: {configId.Key} Duplicate!");
        }

        //遍历配置
        DbContext.DbConfigs?.ForEach(it =>
        {
            var connection = DbContext.Db.GetConnection(it.ConfigId);//获取数据库连接对象

            if (it.InitTable == true)
                connection.DbMaintenance.CreateDatabase();//创建数据库,如果存在则不创建
        });
        var fullName = Assembly.GetExecutingAssembly().FullName;//获取程序集全名
        CodeFirstUtils.CodeFirst(fullName!);//CodeFirst



        //10.4.9 删除logenable
        try
        {
            using var db = DbContext.GetDB<Channel>();
            if (db.DbMaintenance.IsAnyColumn(nameof(Channel), "LogEnable", false))
            {
                var tables = db.DbMaintenance.DropColumn(nameof(Channel), "LogEnable");
            }
        }
        catch { }
        try
        {
            using var db = DbContext.GetDB<Device>();
            if (db.DbMaintenance.IsAnyColumn(nameof(Device), "LogEnable", false))
            {
                var tables = db.DbMaintenance.DropColumn(nameof(Device), "LogEnable");
            }
        }
        catch { }

        serviceProvider.GetService<IHostApplicationLifetime>().ApplicationStarted.Register(() =>
        {
            serviceProvider.GetService<ILoggerFactory>().CreateLogger(nameof(Matrix)).LogInformation("Matrix is started...");
        });
        serviceProvider.GetService<IHostApplicationLifetime>().ApplicationStopping.Register(() =>
        {
            serviceProvider.GetService<ILoggerFactory>().CreateLogger(nameof(Matrix)).LogInformation("Matrix is stopping...");
        });
    }
}

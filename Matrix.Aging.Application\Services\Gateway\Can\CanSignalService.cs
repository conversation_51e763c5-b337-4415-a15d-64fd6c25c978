using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN信号服务实现
/// </summary>
internal sealed class CanSignalService : BaseService<CanSignal>, ICanSignalService
{
    /// <summary>
    /// 根据ID获取CAN信号
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>CAN信号</returns>
    public async Task<CanSignal?> GetCanSignalByIdAsync(long id)
    {
        using var db = GetDB();
        return await db.Queryable<CanSignal>().FirstAsync(x => x.Id == id).ConfigureAwait(false);
    }

    /// <summary>
    /// 根据ID获取CAN信号（同步方法）
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>CAN信号</returns>
    public CanSignal? GetCanSignalById(long id)
    {
        using var db = GetDB();
        return db.Queryable<CanSignal>().First(x => x.Id == id);
    }

    /// <summary>
    /// 根据CanMessage ID获取所有CAN信号
    /// </summary>
    /// <param name="canMessageId">CanMessage ID</param>
    /// <returns>CAN信号列表</returns>
    public async Task<List<CanSignal>> GetCanSignalsByCanMessageIdAsync(long canMessageId)
    {
        using var db = GetDB();
        return await db.Queryable<CanSignal>().Where(x => x.CanMessageId == canMessageId).ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 根据CanBms ID获取所有CAN信号
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>CAN信号列表</returns>
    public async Task<List<CanSignal>> GetCanSignalsByCanBmsIdAsync(long canBmsId)
    {
        // 通过CanMessage关联查询
        var canMessageService = App.GetService<ICanMessageService>()!;
        var canMessages = await canMessageService.GetCanMessagesByCanBmsIdAsync(canBmsId).ConfigureAwait(false);
        var canMessageIds = canMessages.Select(x => x.Id).ToList();

        if (canMessageIds.Count == 0)
            return new List<CanSignal>();

        using var db = GetDB();
        return await db.Queryable<CanSignal>().Where(x => canMessageIds.Contains(x.CanMessageId)).ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 根据信号名称获取CAN信号
    /// </summary>
    /// <param name="canMessageId">CanMessage ID</param>
    /// <param name="signalName">信号名称</param>
    /// <returns>CAN信号</returns>
    public async Task<CanSignal?> GetCanSignalByNameAsync(long canMessageId, string signalName)
    {
        using var db = GetDB();
        return await db.Queryable<CanSignal>()
            .Where(x => x.CanMessageId == canMessageId && x.SignalName == signalName)
            .FirstAsync().ConfigureAwait(false);
    }

    /// <summary>
    /// 创建CAN信号
    /// </summary>
    /// <param name="canSignal">CAN信号</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateCanSignalAsync(CanSignal canSignal)
    {
        using var db = GetDB();
        return await db.Insertable(canSignal).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 更新CAN信号
    /// </summary>
    /// <param name="canSignal">CAN信号</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateCanSignalAsync(CanSignal canSignal)
    {
        using var db = GetDB();
        return await db.Updateable(canSignal).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 删除CAN信号
    /// </summary>
    /// <param name="id">信号ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanSignalAsync(long id)
    {
        using var db = GetDB();
        return await db.Deleteable<CanSignal>().Where(x => x.Id == id).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 批量创建CAN信号
    /// </summary>
    /// <param name="canSignals">CAN信号列表</param>
    /// <returns>创建结果</returns>
    public async Task<bool> BatchCreateCanSignalsAsync(List<CanSignal> canSignals)
    {
        using var db = GetDB();
        return await db.Insertable(canSignals).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 根据CanMessage ID删除所有CAN信号
    /// </summary>
    /// <param name="canMessageId">CanMessage ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanSignalsByCanMessageIdAsync(long canMessageId)
    {
        using var db = GetDB();
        return await db.Deleteable<CanSignal>().Where(x => x.CanMessageId == canMessageId).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 根据CanBms ID删除所有CAN信号
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteCanSignalsByCanBmsIdAsync(long canBmsId)
    {
        var canMessageService = App.GetService<ICanMessageService>()!;
        var canMessages = await canMessageService.GetCanMessagesByCanBmsIdAsync(canBmsId).ConfigureAwait(false);
        var canMessageIds = canMessages.Select(x => x.Id).ToList();

        if (canMessageIds.Count == 0)
            return true;

        using var db = GetDB();
        return await db.Deleteable<CanSignal>().Where(x => canMessageIds.Contains(x.CanMessageId)).ExecuteCommandAsync().ConfigureAwait(false) > 0;
    }

    /// <summary>
    /// 获取信号的当前值（从运行时获取）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值</returns>
    public async Task<double?> GetCanSignalValueAsync(long deviceId, string signalName)
    {
        try
        {
            // 从GlobalData获取设备运行时
            if (GlobalData.IdDevices.TryGetValue(deviceId, out var deviceRuntime))
            {
                if (deviceRuntime?.Driver is CollectBase collectDriver)
                {
                    return collectDriver.GetCanSignalValue(signalName);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            App.GetService<ILogger<CanSignalService>>()?.LogError(ex, $"获取CAN信号值失败: {signalName}");
            return null;
        }
    }

    /// <summary>
    /// 设置信号值（到运行时）
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetCanSignalValueAsync(long channelId, string signalName, double value)
    {
        try
        {
            // 从GlobalData获取通道运行时
            if (GlobalData.ReadOnlyChannels.TryGetValue(channelId, out var channelRuntime))
            {
                if (channelRuntime.ChannelType == ChannelTypeEnum.CAN && channelRuntime.CanBmsId.HasValue)
                {
                    // 这里需要从Channel的CAN运行时中获取CanBmsRuntime
                    // 暂时返回false，需要在实际运行时架构中完善
                    return false;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            App.GetService<ILogger<CanSignalService>>()?.LogError(ex, $"设置CAN信号值失败: {signalName}={value}");
            return false;
        }
    }

    /// <summary>
    /// 设置信号值并发送CAN报文
    /// </summary>
    /// <param name="channelId">通道ID</param>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>设置和发送结果</returns>
    public async Task<bool> SetCanSignalValueAndSendAsync(long channelId, string signalName, double value)
    {
        try
        {
            // 从GlobalData获取通道运行时
            if (GlobalData.ReadOnlyChannels.TryGetValue(channelId, out var channelRuntime))
            {
                if (channelRuntime.ChannelType == ChannelTypeEnum.CAN && channelRuntime.CanBmsId.HasValue)
                {
                    // 这里需要从Channel的CAN运行时中获取CanBmsRuntime
                    // 设置信号值并构建发送数据
                    // 然后通过Channel发送CAN报文
                    // 暂时返回false，需要在实际运行时架构中完善
                    return false;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            App.GetService<ILogger<CanSignalService>>()?.LogError(ex, $"设置CAN信号值并发送失败: {signalName}={value}");
            return false;
        }
    }
}

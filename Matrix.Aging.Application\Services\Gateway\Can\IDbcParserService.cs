namespace Matrix.Aging.Application;

/// <summary>
/// DBC文件解析服务接口
/// </summary>
public interface IDbcParserService
{
    /// <summary>
    /// 解析DBC文件
    /// </summary>
    /// <param name="dbcFilePath">DBC文件路径</param>
    /// <returns>解析结果</returns>
    Task<DbcParseResult> ParseDbcFileAsync(string dbcFilePath);

    /// <summary>
    /// 验证DBC文件格式
    /// </summary>
    /// <param name="dbcFilePath">DBC文件路径</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateDbcFileAsync(string dbcFilePath);

    /// <summary>
    /// 导入DBC数据到数据库（创建CanBms和CanSignal）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="dbcParseResult">DBC解析结果</param>
    /// <param name="importOptions">导入选项</param>
    /// <returns>导入结果</returns>
    Task<DbcImportResult> ImportDbcDataAsync(long deviceId, DbcParseResult dbcParseResult, DbcImportOptions importOptions);

    /// <summary>
    /// 获取DBC文件信息
    /// </summary>
    /// <param name="dbcFilePath">DBC文件路径</param>
    /// <returns>文件信息</returns>
    Task<DbcFileInfo> GetDbcFileInfoAsync(string dbcFilePath);
}

/// <summary>
/// DBC解析结果
/// </summary>
public class DbcParseResult
{
    /// <summary>
    /// 解析是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// DBC文件路径
    /// </summary>
    public string DbcFilePath { get; set; } = string.Empty;

    /// <summary>
    /// 解析出的BMS列表（每个报文对应一个BMS）
    /// </summary>
    public List<DbcBms> BmsList { get; set; } = new();

    /// <summary>
    /// DBC文件版本信息
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// DBC文件描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// DBC BMS信息（对应一个报文及其信号）
/// </summary>
public class DbcBms
{
    /// <summary>
    /// BMS名称（通常使用报文名称）
    /// </summary>
    public string BmsName { get; set; } = string.Empty;

    /// <summary>
    /// 接收报文ID
    /// </summary>
    public uint ReceiveMessageId { get; set; }

    /// <summary>
    /// 接收报文名称
    /// </summary>
    public string? ReceiveMessageName { get; set; }

    /// <summary>
    /// 接收报文DLC
    /// </summary>
    public byte ReceiveDLC { get; set; }

    /// <summary>
    /// 接收报文是否扩展帧
    /// </summary>
    public bool ReceiveIsExtended { get; set; }

    /// <summary>
    /// 接收报文是否CAN FD
    /// </summary>
    public bool ReceiveIsCanFd { get; set; }

    /// <summary>
    /// 发送节点
    /// </summary>
    public string? Transmitter { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 信号列表
    /// </summary>
    public List<DbcSignal> Signals { get; set; } = new();
}

/// <summary>
/// DBC信号信息
/// </summary>
public class DbcSignal
{
    /// <summary>
    /// 信号名称
    /// </summary>
    public string SignalName { get; set; } = string.Empty;



    /// <summary>
    /// 起始位
    /// </summary>
    public ushort StartBit { get; set; }

    /// <summary>
    /// 位长度
    /// </summary>
    public byte BitLength { get; set; }

    /// <summary>
    /// 字节序（0=Intel, 1=Motorola）
    /// </summary>
    public CanByteOrderEnum ByteOrder { get; set; }

    /// <summary>
    /// 是否有符号
    /// </summary>
    public bool IsSigned { get; set; }

    /// <summary>
    /// 分辨率
    /// </summary>
    public double Resolution { get; set; } = 1.0;

    /// <summary>
    /// 偏移量
    /// </summary>
    public double Offset { get; set; } = 0.0;

    /// <summary>
    /// 最小值
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 接收节点列表
    /// </summary>
    public List<string> Receivers { get; set; } = new();

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 初始值
    /// </summary>
    public double? InitialValue { get; set; }
}

/// <summary>
/// DBC导入选项
/// </summary>
public class DbcImportOptions
{
    /// <summary>
    /// 是否覆盖现有数据
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;

    /// <summary>
    /// 是否只导入接收报文
    /// </summary>
    public bool OnlyReceiveMessages { get; set; } = true;



    /// <summary>
    /// 变量名称前缀
    /// </summary>
    public string? VariablePrefix { get; set; }

    /// <summary>
    /// 是否启用所有导入的信号
    /// </summary>
    public bool EnableAllSignals { get; set; } = true;

    /// <summary>
    /// 是否显示所有导入的信号
    /// </summary>
    public bool DisplayAllSignals { get; set; } = true;

    /// <summary>
    /// 是否保存所有导入的信号
    /// </summary>
    public bool SaveAllSignals { get; set; } = true;

    /// <summary>
    /// 指定要导入的报文ID列表（为空则导入所有）
    /// </summary>
    public List<uint>? SpecificMessageIds { get; set; }

    /// <summary>
    /// 指定要导入的信号名称列表（为空则导入所有）
    /// </summary>
    public List<string>? SpecificSignalNames { get; set; }
}

/// <summary>
/// DBC导入结果
/// </summary>
public class DbcImportResult
{
    /// <summary>
    /// 导入是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 导入的BMS数量
    /// </summary>
    public int ImportedBmsCount { get; set; }

    /// <summary>
    /// 导入的信号数量
    /// </summary>
    public int ImportedSignalCount { get; set; }

    /// <summary>
    /// 创建的CAN变量数量
    /// </summary>
    public int CreatedCanVariableCount { get; set; }

    /// <summary>
    /// 跳过的项目数量
    /// </summary>
    public int SkippedCount { get; set; }

    /// <summary>
    /// 详细信息
    /// </summary>
    public List<string> Details { get; set; } = new();
}

/// <summary>
/// DBC文件信息
/// </summary>
public class DbcFileInfo
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedTime { get; set; }

    /// <summary>
    /// 报文数量
    /// </summary>
    public int MessageCount { get; set; }

    /// <summary>
    /// 信号数量
    /// </summary>
    public int SignalCount { get; set; }

    /// <summary>
    /// 节点数量
    /// </summary>
    public int NodeCount { get; set; }

    /// <summary>
    /// DBC版本
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// 是否有效的DBC文件
    /// </summary>
    public bool IsValid { get; set; }
}

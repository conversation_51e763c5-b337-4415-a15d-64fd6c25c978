﻿
using Microsoft.AspNetCore.Components.Forms;

using Matrix.Aging.Application;

namespace Matrix.Aging.Razor;

public partial class ChannelEditComponent
{
    [Inject]
    IStringLocalizer<Matrix.Aging.Razor._Imports> _Localizer { get; set; }

    [Parameter]
    public bool BatchEditEnable { get; set; }

    [Parameter]
    [EditorRequired]
    public Channel Model { get; set; }

    [Parameter]
    public Func<Task> OnValidSubmit { get; set; }

    [Parameter]
    public bool ValidateEnable { get; set; }

    [CascadingParameter]
    private Func<Task>? OnCloseAsync { get; set; }

    public async Task ValidSubmit(EditContext editContext)
    {
        try
        {
            if (OnValidSubmit != null)
                await OnValidSubmit.Invoke();
            if (OnCloseAsync != null)
                await OnCloseAsync();
            await ToastService.Default();
        }
        catch (Exception ex)
        {
            await ToastService.Warn(ex);
        }
    }

    public Dictionary<string, PluginInfo> PluginDcit { get; set; }

    public IEnumerable<SelectedItem> PluginNames { get; set; }

    public IEnumerable<SelectedItem> CanBmsItems { get; set; } = Enumerable.Empty<SelectedItem>();

    [Parameter]
    public PluginTypeEnum? PluginType { get; set; }

    protected override async Task OnInitializedAsync()
    {
        PluginNames = GlobalData.PluginService.GetList(PluginType).BuildPluginSelectList();
        PluginDcit = GlobalData.PluginService.GetList(PluginType).ToDictionary(a => a.FullName);
        
        // 加载CanBms选项
        await LoadCanBmsItemsAsync();
        
        await base.OnInitializedAsync();
    }

    /// <summary>
    /// 加载CanBms选项
    /// </summary>
    private async Task LoadCanBmsItemsAsync()
    {
        try
        {
            var canBmsService = App.GetService<ICanBmsService>();
            if (canBmsService != null)
            {
                var allCanBms = await canBmsService.GetAllCanBmsAsync();
                CanBmsItems = new List<SelectedItem> { new SelectedItem("", _Localizer["None"]) }
                    .Concat(allCanBms.Select(bms => new SelectedItem(bms.Id.ToString(), bms.BmsName)));
            }
        }
        catch (Exception ex)
        {
            await ToastService.Warn(ex);
        }
    }

    /// <summary>
    /// 获取CanBms显示文本
    /// </summary>
    /// <param name="canBmsId">CanBms ID</param>
    /// <returns>显示文本</returns>
    private string GetCanBmsDisplayText(long? canBmsId)
    {
        if (!canBmsId.HasValue)
            return _Localizer["None"];
            
        var item = CanBmsItems.FirstOrDefault(x => x.Value == canBmsId.ToString());
        return item?.Text ?? canBmsId.ToString();
    }

    /// <summary>
    /// 添加CanBms
    /// </summary>
    private async Task AddCanBms()
    {
        try
        {
            // 这里可以打开CanBms编辑对话框
            await ToastService.Information("CAN BMS协议文件管理功能待实现");
            // TODO: 实现CanBms编辑功能
        }
        catch (Exception ex)
        {
            await ToastService.Warn(ex);
        }
    }
    protected override Task OnParametersSetAsync()
    {
        return base.OnParametersSetAsync();
    }
}

using Matrix.Foundation.HXPower;
using System;
using System.Threading.Tasks;

namespace Examples;

/// <summary>
/// HXPower设备使用示例
/// </summary>
public class HXPowerUsageExample
{
    /// <summary>
    /// 演示HXPower设备的正确使用方式
    /// </summary>
    public static async Task RunUsageExampleAsync()
    {
        Console.WriteLine("=== HXPower设备使用示例 ===");
        
        // 1. 数据处理示例
        Console.WriteLine("\n--- 数据处理示例 ---");
        DemonstrateDataProcessing();
        
        // 2. 控制命令生成示例
        Console.WriteLine("\n--- 控制命令生成示例 ---");
        DemonstrateControlCommandGeneration();
        
        // 3. 协议解析示例
        Console.WriteLine("\n--- 协议解析示例 ---");
        DemonstrateProtocolParsing();
        
        Console.WriteLine("\n=== 示例完成 ===");
    }
    
    /// <summary>
    /// 演示数据处理
    /// </summary>
    private static void DemonstrateDataProcessing()
    {
        // 模拟接收到的数据报文
        uint canId = 0x0810FF41; // 模块1通道1
        byte[] data = GenerateTestDataFrame(
            HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent, 
            12.5, // 12.5V
            3.2   // 3.2A
        );
        
        Console.WriteLine($"接收CAN数据: ID=0x{canId:X8}, Data={BitConverter.ToString(data)}");
        
        // 使用数据处理器解析
        if (HXPowerDataProcessor.IsDataFrame(canId))
        {
            var deviceStatus = HXPowerDataProcessor.ParseDataFrame(canId, data);
            Console.WriteLine($"解析结果:");
            Console.WriteLine($"  模块: {deviceStatus.ModuleNumber}");
            Console.WriteLine($"  通道: {deviceStatus.ChannelNumber}");
            Console.WriteLine($"  工作模式: {deviceStatus.DeviceStatus}");
            Console.WriteLine($"  电压: {deviceStatus.Voltage:F5}V");
            Console.WriteLine($"  电流: {deviceStatus.Current:F5}A");
        }
    }
    
    /// <summary>
    /// 演示控制命令生成
    /// </summary>
    private static void DemonstrateControlCommandGeneration()
    {
        // 创建控制命令
        var command = new HXPowerDataProcessor.HXPowerControlCommand
        {
            ModuleNumber = 1,
            ChannelNumber = 1,
            WorkMode = HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent,
            ControlValue = 5.0 // 5A
        };
        
        // 生成CAN数据
        var canId = command.GetCanId();
        var commandData = HXPowerDataProcessor.GenerateControlCommandData(command);
        
        Console.WriteLine($"生成控制命令:");
        Console.WriteLine($"  模块: {command.ModuleNumber}");
        Console.WriteLine($"  通道: {command.ChannelNumber}");
        Console.WriteLine($"  工作模式: {command.WorkMode}");
        Console.WriteLine($"  控制值: {command.ControlValue:F5}A");
        Console.WriteLine($"  CAN ID: 0x{canId:X8}");
        Console.WriteLine($"  CAN数据: {BitConverter.ToString(commandData)}");
    }
    
    /// <summary>
    /// 演示协议解析
    /// </summary>
    private static void DemonstrateProtocolParsing()
    {
        // 测试各种CAN ID识别
        var testCanIds = new uint[]
        {
            0x0810FF41, // 数据报文
            0x1020FF02, // 心跳报文
            0x04904102, // 控制命令
            0x1040ff41, // 故障报文
            0x12345678  // 其他报文
        };
        
        foreach (var canId in testCanIds)
        {
            Console.WriteLine($"CAN ID 0x{canId:X8}:");
            Console.WriteLine($"  数据报文: {HXPowerDataProcessor.IsDataFrame(canId)}");
            Console.WriteLine($"  心跳报文: {HXPowerDataProcessor.IsHeartbeatFrame(canId)}");
            Console.WriteLine($"  控制命令: {HXPowerDataProcessor.IsControlFrame(canId)}");
            Console.WriteLine($"  故障报文: {HXPowerDataProcessor.IsFaultFrame(canId)}");
            
            if (HXPowerDataProcessor.IsDataFrame(canId))
            {
                var (moduleNum, channelNum) = HXPowerDataProcessor.GetModuleChannelFromDataCanId(canId);
                Console.WriteLine($"  → 模块{moduleNum}通道{channelNum}");
            }
        }
        
        // 测试心跳解析
        var heartbeatData = new byte[] { 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
        var lifeFrame = HXPowerDataProcessor.ParseHeartbeatFrame(heartbeatData);
        Console.WriteLine($"心跳解析: 生命帧=0x{lifeFrame:X2}");
        
        // 测试故障解析
        var faultData = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 };
        var (faultModule, faultChannel, faultCode) = HXPowerDataProcessor.ParseFaultFrame(0x1040ff41, faultData);
        Console.WriteLine($"故障解析: 模块{faultModule}通道{faultChannel}, 故障码=0x{faultCode:X16}");
    }
    
    /// <summary>
    /// 生成测试数据帧
    /// </summary>
    private static byte[] GenerateTestDataFrame(HXPowerDataProcessor.HXPowerWorkMode workMode, double voltage, double current)
    {
        var data = new byte[8];
        
        // 第1字节：设备状态
        data[0] = (byte)workMode;
        
        // 第2-4字节：电压（分辨率0.00001V）
        var voltageRaw = (uint)(voltage / 0.00001);
        var voltageBytes = BitConverter.GetBytes(voltageRaw);
        Array.Copy(voltageBytes, 0, data, 1, 3);
        
        // 第5-8字节：电流（分辨率0.00001A，有符号）
        var currentRaw = (int)(current / 0.00001);
        var currentBytes = BitConverter.GetBytes(currentRaw);
        Array.Copy(currentBytes, 0, data, 4, 4);
        
        return data;
    }
    
    /// <summary>
    /// 演示设备使用的正确架构
    /// </summary>
    public static void DemonstrateCorrectArchitecture()
    {
        Console.WriteLine("=== 正确的HXPower设备架构 ===");
        Console.WriteLine();
        
        Console.WriteLine("1. 创建Channel处理HXPower协议解析");
        Console.WriteLine("   - Channel接收CAN数据");
        Console.WriteLine("   - 使用HXPowerDataProcessor解析协议");
        Console.WriteLine("   - 更新设备状态和变量");
        Console.WriteLine();
        
        Console.WriteLine("2. 通过Device进行控制");
        Console.WriteLine("   - 给设备变量赋值（模块号、通道号、工作模式、控制值）");
        Console.WriteLine("   - 调用HXPower插件处理数据");
        Console.WriteLine("   - 通过Channel发送CAN报文");
        Console.WriteLine();
        
        Console.WriteLine("3. 数据流程");
        Console.WriteLine("   接收: CAN → Channel → HXPowerDataProcessor → 设备状态");
        Console.WriteLine("   发送: 设备变量 → HXPowerDataProcessor → Channel → CAN");
        Console.WriteLine();
        
        Console.WriteLine("4. 插件职责");
        Console.WriteLine("   - Matrix.Foundation.HXPower: 协议解析和数据处理");
        Console.WriteLine("   - Matrix.PowerPlugin.HXPower: 设备控制逻辑");
        Console.WriteLine("   - Channel: CAN通信和报文分发");
    }
} 
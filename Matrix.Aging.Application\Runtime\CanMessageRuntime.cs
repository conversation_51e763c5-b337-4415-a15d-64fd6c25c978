using Matrix.Foundation;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN报文运行时管理类
/// </summary>
public class CanMessageRuntime
{
    /// <summary>
    /// CanMessage配置
    /// </summary>
    public CanMessage CanMessage { get; private set; }

    /// <summary>
    /// 关联的信号列表（按解析顺序排序）
    /// </summary>
    public CanSignal[] Signals { get; private set; } = Array.Empty<CanSignal>();

    /// <summary>
    /// 信号值数组（与Signals数组对应）
    /// </summary>
    public double[] SignalValues { get; private set; } = Array.Empty<double>();

    /// <summary>
    /// 信号在线状态数组（与Signals数组对应）
    /// </summary>
    public bool[] SignalOnlineStatus { get; private set; } = Array.Empty<bool>();

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastReceiveTime { get; set; }

    /// <summary>
    /// 接收计数
    /// </summary>
    public long ReceiveCount { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="canMessage">CanMessage配置</param>
    public CanMessageRuntime(CanMessage canMessage)
    {
        CanMessage = canMessage;
    }

    /// <summary>
    /// 初始化信号
    /// </summary>
    /// <param name="signals">信号列表</param>
    public void Initialize(List<CanSignal> signals)
    {
        // 按起始位排序，优化解析性能
        Signals = signals.Where(s => s.Enable).OrderBy(s => s.StartBit).ToArray();
        SignalValues = new double[Signals.Length];
        SignalOnlineStatus = new bool[Signals.Length];
    }

    /// <summary>
    /// 解析CAN数据到所有信号
    /// </summary>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    public void ParseCanData(ReadOnlySpan<byte> canData, DateTime timestamp)
    {
        if (canData.Length == 0)
            return;

        LastReceiveTime = timestamp;
        ReceiveCount++;
        IsOnline = true;

        // 高效解析所有信号
        for (int i = 0; i < Signals.Length; i++)
        {
            var signal = Signals[i];
            try
            {
                // 验证信号参数
                if (!CanSignalParser.ValidateSignalParameters(signal.StartBit, signal.BitLength, canData.Length))
                {
                    SignalOnlineStatus[i] = false;
                    continue;
                }

                // 解析信号值
                SignalValues[i] = CanSignalParser.ParseSignal(
                    canData, 
                    signal.StartBit, 
                    signal.BitLength,
                    signal.ByteOrder == CanByteOrderEnum.Motorola,
                    signal.IsSigned,
                    signal.Resolution,
                    signal.Offset);

                SignalOnlineStatus[i] = true;
            }
            catch
            {
                SignalOnlineStatus[i] = false;
            }
        }
    }

    /// <summary>
    /// 构建发送数据
    /// </summary>
    /// <param name="signalValues">信号值字典</param>
    /// <returns>CAN数据</returns>
    public byte[] BuildSendData(Dictionary<string, double> signalValues)
    {
        var data = new byte[CanMessage.DLC];
        var dataSpan = data.AsSpan();

        for (int i = 0; i < Signals.Length; i++)
        {
            var signal = Signals[i];
            if (signalValues.TryGetValue(signal.SignalName, out double value))
            {
                try
                {
                    CanSignalParser.WriteSignal(
                        dataSpan,
                        value,
                        signal.StartBit,
                        signal.BitLength,
                        signal.ByteOrder == CanByteOrderEnum.Motorola,
                        signal.Resolution,
                        signal.Offset);
                }
                catch
                {
                    // 写入失败，忽略该信号
                }
            }
        }

        return data;
    }

    /// <summary>
    /// 获取所有信号值
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetAllSignalValues()
    {
        var result = new Dictionary<string, double>();
        for (int i = 0; i < Signals.Length; i++)
        {
            if (SignalOnlineStatus[i])
            {
                result[Signals[i].SignalName] = SignalValues[i];
            }
        }
        return result;
    }

    /// <summary>
    /// 获取指定信号的值
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>信号值，如果不存在或离线返回null</returns>
    public double? GetSignalValue(string signalName)
    {
        for (int i = 0; i < Signals.Length; i++)
        {
            if (Signals[i].SignalName == signalName && SignalOnlineStatus[i])
            {
                return SignalValues[i];
            }
        }
        return null;
    }

    /// <summary>
    /// 检查是否包含指定信号
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>是否包含该信号</returns>
    public bool HasSignal(string signalName)
    {
        for (int i = 0; i < Signals.Length; i++)
        {
            if (Signals[i].SignalName == signalName)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 检查信号是否在线
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <returns>是否在线</returns>
    public bool IsSignalOnline(string signalName)
    {
        for (int i = 0; i < Signals.Length; i++)
        {
            if (Signals[i].SignalName == signalName)
            {
                return SignalOnlineStatus[i];
            }
        }
        return false;
    }

    /// <summary>
    /// 设置信号值
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否设置成功</returns>
    public bool SetSignalValue(string signalName, double value)
    {
        for (int i = 0; i < Signals.Length; i++)
        {
            if (Signals[i].SignalName == signalName)
            {
                SignalValues[i] = value;
                SignalOnlineStatus[i] = true; // 设置后标记为在线
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 获取当前所有信号值（包括设置的值）
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetCurrentSignalValues()
    {
        var result = new Dictionary<string, double>();
        for (int i = 0; i < Signals.Length; i++)
        {
            result[Signals[i].SignalName] = SignalValues[i];
        }
        return result;
    }

    /// <summary>
    /// 根据当前信号值构建完整的发送数据
    /// </summary>
    /// <returns>CAN数据</returns>
    public byte[] BuildCurrentSendData()
    {
        var currentValues = GetCurrentSignalValues();
        return BuildSendData(currentValues);
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public CanMessageStatistics GetStatistics()
    {
        return new CanMessageStatistics
        {
            MessageId = CanMessage.MessageId,
            MessageName = CanMessage.MessageName,
            LastReceiveTime = LastReceiveTime,
            ReceiveCount = ReceiveCount,
            IsOnline = IsOnline,
            TotalSignalCount = Signals.Length,
            OnlineSignalCount = SignalOnlineStatus.Count(s => s),
            SignalOnlineRate = Signals.Length > 0 ? (double)SignalOnlineStatus.Count(s => s) / Signals.Length : 0
        };
    }
}

/// <summary>
/// CAN报文统计信息
/// </summary>
public class CanMessageStatistics
{
    /// <summary>
    /// 报文ID
    /// </summary>
    public uint MessageId { get; set; }

    /// <summary>
    /// 报文名称
    /// </summary>
    public string MessageName { get; set; } = string.Empty;

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime LastReceiveTime { get; set; }

    /// <summary>
    /// 接收计数
    /// </summary>
    public long ReceiveCount { get; set; }

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }

    /// <summary>
    /// 总信号数量
    /// </summary>
    public int TotalSignalCount { get; set; }

    /// <summary>
    /// 在线信号数量
    /// </summary>
    public int OnlineSignalCount { get; set; }

    /// <summary>
    /// 信号在线率
    /// </summary>
    public double SignalOnlineRate { get; set; }
} 
using Matrix.Aging.Application;
using Matrix.Foundation;
using System.Collections.Concurrent;

namespace Examples;

/// <summary>
/// 测试用CAN设备插件
/// 用于验证CAN通信架构的基本功能
/// </summary>
public class TestCanDevice : CanCollectBase
{
    private readonly TestCanProperty _properties = new();
    private readonly Timer? _simulationTimer;
    private readonly Random _random = new();

    /// <summary>
    /// 设备处理的CAN ID列表（模拟BMS数据）
    /// </summary>
    public override IEnumerable<uint> CanIds => new uint[] 
    { 
        0x100, // 电池状态信息
        0x101, // 电池报警信息
        0x200, // 高压电池包状态
        0x201, // 单体电池信息
        0x300  // 低压系统状态
    };

    /// <summary>
    /// 采集属性
    /// </summary>
    public override CollectPropertyBase CollectProperties => _properties;

    /// <summary>
    /// 构造函数
    /// </summary>
    public TestCanDevice()
    {
        // 启动数据模拟定时器（每秒发送一次模拟数据）
        _simulationTimer = new Timer(SimulateCanData, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 模拟CAN数据发送
    /// </summary>
    /// <param name="state">定时器状态</param>
    private async void SimulateCanData(object? state)
    {
        try
        {
            if (!IsCanChannel() || CurrentChannel == null)
                return;

            // 模拟电池状态信息 (0x100)
            await SimulateBatteryStatus();

            // 模拟电池报警信息 (0x101) - 偶尔发送
            if (_random.Next(10) < 2) // 20%概率
            {
                await SimulateBatteryAlarms();
            }

            // 模拟高压电池包状态 (0x200)
            await SimulateHighVoltageBatteryStatus();

            // 模拟单体电池信息 (0x201)
            await SimulateCellBatteryInfo();

            // 模拟低压系统状态 (0x300)
            await SimulateLowVoltageSystemStatus();
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, "模拟CAN数据发送失败");
        }
    }

    /// <summary>
    /// 模拟电池状态信息
    /// </summary>
    private async Task SimulateBatteryStatus()
    {
        var data = new byte[8];
        var voltage = (ushort)(_random.Next(3000, 4200)); // 30.00V - 42.00V
        var current = (short)(_random.Next(-1000, 1000)); // -100A - 100A
        var temperature = (byte)(_random.Next(65, 105)); // 25°C - 65°C (offset +40)
        var soc = (byte)(_random.Next(0, 200)); // 0% - 100% (resolution 0.5)
        var status = (byte)(_random.Next(0, 16)); // 状态标志

        // 按照Intel字节序打包数据
        data[0] = (byte)(voltage & 0xFF);
        data[1] = (byte)((voltage >> 8) & 0xFF);
        data[2] = (byte)(current & 0xFF);
        data[3] = (byte)((current >> 8) & 0xFF);
        data[4] = temperature;
        data[5] = soc;
        data[6] = status;
        data[7] = 0x00; // 保留字节

        await SendCanDataAsync(0x100, data);
    }

    /// <summary>
    /// 模拟电池报警信息
    /// </summary>
    private async Task SimulateBatteryAlarms()
    {
        var data = new byte[8];
        var alarms = (byte)_random.Next(0, 16); // 随机报警位

        data[0] = alarms;
        // 其余字节保留为0

        await SendCanDataAsync(0x101, data);
    }

    /// <summary>
    /// 模拟高压电池包状态
    /// </summary>
    private async Task SimulateHighVoltageBatteryStatus()
    {
        var data = new byte[8];
        var voltage = (ushort)(_random.Next(3000, 4200)); // 300V - 420V
        var current = (short)(_random.Next(-1000, 1000)); // -100A - 100A
        var soc = (byte)(_random.Next(0, 200)); // 0% - 100%

        data[0] = (byte)(voltage & 0xFF);
        data[1] = (byte)((voltage >> 8) & 0xFF);
        data[2] = (byte)(current & 0xFF);
        data[3] = (byte)((current >> 8) & 0xFF);
        data[4] = soc;

        await SendCanDataAsync(0x200, data);
    }

    /// <summary>
    /// 模拟单体电池信息
    /// </summary>
    private async Task SimulateCellBatteryInfo()
    {
        var data = new byte[8];
        var cellVoltage1 = (ushort)(_random.Next(3500, 4200)); // 3.5V - 4.2V (mV)
        var cellVoltage2 = (ushort)(_random.Next(3500, 4200)); // 3.5V - 4.2V (mV)
        var cellTemp1 = (byte)(_random.Next(65, 105)); // 25°C - 65°C

        // 打包12位数据
        data[0] = (byte)(cellVoltage1 & 0xFF);
        data[1] = (byte)(((cellVoltage1 >> 8) & 0x0F) | ((cellVoltage2 & 0x0F) << 4));
        data[2] = (byte)((cellVoltage2 >> 4) & 0xFF);
        data[3] = cellTemp1;

        await SendCanDataAsync(0x201, data);
    }

    /// <summary>
    /// 模拟低压系统状态
    /// </summary>
    private async Task SimulateLowVoltageSystemStatus()
    {
        var data = new byte[8];
        var voltage = (byte)(_random.Next(110, 140)); // 11V - 14V
        var current = (byte)(_random.Next(0, 100)); // 0A - 10A

        data[0] = voltage;
        data[1] = current;

        await SendCanDataAsync(0x300, data);
    }

    /// <summary>
    /// 数据读取实现（CAN设备不需要主动读取）
    /// </summary>
    protected override async ValueTask<OperResult<byte[]>> ReadSourceAsync(
        VariableSourceRead variableSourceRead, CancellationToken cancellationToken)
    {
        // CAN设备是被动接收数据，不需要主动读取
        await Task.CompletedTask;
        return new OperResult<byte[]>("CAN设备不支持主动读取操作");
    }

    /// <summary>
    /// 写入实现（可以用于发送控制指令）
    /// </summary>
    protected override async ValueTask<OperResult> WriteSourceAsync(
        VariableSourceRead variableSourceRead, object value, CancellationToken cancellationToken)
    {
        try
        {
            // 解析CAN地址格式：例如 "0x180:ControlCmd"
            var parts = variableSourceRead.Address.Split(':');
            if (parts.Length != 2)
            {
                return new OperResult("CAN地址格式错误，应为 'CanId:SignalName'");
            }

            var canId = Convert.ToUInt32(parts[0], 16);
            var signalName = parts[1];

            // 构建信号值字典
            var signalValues = new Dictionary<string, double>
            {
                { signalName, Convert.ToDouble(value) }
            };

            // 发送CAN信号数据
            var success = await SendCanSignalDataAsync(canId, signalValues);
            return success ? new OperResult() : new OperResult("发送CAN数据失败");
        }
        catch (Exception ex)
        {
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _simulationTimer?.Dispose();
        }
        base.Dispose(disposing);
    }
}

/// <summary>
/// 测试CAN设备属性
/// </summary>
public class TestCanProperty : CollectPropertyBase
{
    /// <summary>
    /// 模拟数据发送间隔（毫秒）
    /// </summary>
    public int SimulationInterval { get; set; } = 1000;

    /// <summary>
    /// 是否启用数据模拟
    /// </summary>
    public bool EnableSimulation { get; set; } = true;

    /// <summary>
    /// 模拟数据变化范围
    /// </summary>
    public double VariationRange { get; set; } = 0.1;
} 
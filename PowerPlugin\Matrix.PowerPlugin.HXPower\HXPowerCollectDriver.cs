using Matrix.Foundation;
using Matrix.Foundation.HXPower;
using Matrix.Aging.Application;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;
using TouchSocket.Core;

namespace Matrix.PowerPlugin.HXPower;

/// <summary>
/// HXPower采集驱动
/// </summary>
public class HXPowerCollectDriver : CollectFoundationBase, ICanSender
{
    private HXPowerDevice? _hxPowerDevice;

    /// <summary>
    /// 插件配置项
    /// </summary>
    public override CollectPropertyBase CollectProperties => new HXPowerCollectProperty();

    /// <summary>
    /// 插件默认的通讯设备
    /// </summary>
    public override IDevice? FoundationDevice => _hxPowerDevice;

    /// <summary>
    /// 初始化通道
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected override async Task InitChannelAsync(IChannel? channel, CancellationToken cancellationToken)
    {
        if (channel == null)
            throw new ArgumentNullException(nameof(channel));

        // 创建HXPower设备
        _hxPowerDevice = new HXPowerDevice();
        _hxPowerDevice.InitChannel(channel, LogMessage);

        LogMessage?.LogInformation($"HXPower采集驱动初始化完成: {CurrentDevice?.Name}");
        
        await base.InitChannelAsync(channel, cancellationToken);
    }

    /// <summary>
    /// 变量打包操作（HXPower不需要读取变量，返回空列表）
    /// </summary>
    /// <param name="deviceVariables">设备变量列表</param>
    /// <returns>源读取变量列表</returns>
    protected override Task<List<VariableSourceRead>> ProtectedLoadSourceReadAsync(List<VariableRuntime> deviceVariables)
    {
        // HXPower主要用于写入控制命令，不需要打包读取变量
        return Task.FromResult(new List<VariableSourceRead>());
    }

    /// <summary>
    /// 执行循环（HXPower不需要主动读取）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected override async ValueTask ProtectedExecuteAsync(CancellationToken cancellationToken)
    {
        // HXPower主要用于写入控制，不需要循环执行
        await Task.CompletedTask;
    }

    /// <summary>
    /// 发送CAN报文（字节数据方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">字节数据</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> SendCanFrameAsync(uint canId, byte[] data)
    {
        try
        {
            if (_hxPowerDevice?.Channel is CanChannel canChannel)
            {
                var success = await canChannel.SendCanFrameAsync(canId, data);
                return success ? OperResult.Success : new OperResult("CAN帧发送失败");
            }
            else
            {
                return new OperResult("当前通道不是CAN通道");
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"HXPower CAN帧发送异常: ID=0x{canId:X8}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 发送CAN报文（信号值方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值字典（信号名 -> 信号值）</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> SendCanSignalAsync(uint canId, Dictionary<string, double> signalValues)
    {
        try
        {
            // HXPower插件暂不支持信号值发送，建议使用字节数据发送
            LogMessage?.LogWarning($"HXPower插件暂不支持信号值发送: ID=0x{canId:X8}");
            return new OperResult("HXPower插件暂不支持信号值发送，请使用字节数据发送");
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"HXPower信号发送异常: ID=0x{canId:X8}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 根据AgStepTypeEnum发送HXPower控制命令
    /// </summary>
    /// <param name="moduleNumber">模块号</param>
    /// <param name="channelNumber">通道号</param>
    /// <param name="stepType">工步类型</param>
    /// <param name="parameters">参数字典</param>
    /// <returns>发送结果</returns>
    public async Task<OperResult> SendHXPowerCommandAsync(int moduleNumber, int channelNumber,
        AgStepTypeEnum stepType, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 转换工步类型为HXPower工作模式
            var workMode = ConvertAgStepTypeToHXPowerMode(stepType);

            // 获取参数值
            var current = GetParameterValue(parameters, "Current", 0.0);
            var voltage = GetParameterValue(parameters, "Voltage", 0.0);
            var power = GetParameterValue(parameters, "Power", 0.0);

            // 生成控制命令数据
            var commandData = HXPowerDataProcessor.GenerateControlCommandData(
                moduleNumber, channelNumber, workMode, current, voltage, power);

            // 计算CAN ID
            var canId = HXPowerDataProcessor.GetControlCanId(moduleNumber, channelNumber);

            // 发送CAN帧
            var result = await SendCanFrameAsync(canId, commandData);

            if (result.IsSuccess)
            {
                LogMessage?.LogInformation($"HXPower工步命令发送成功: 模块{moduleNumber}通道{channelNumber}, 工步={stepType}, 模式={workMode}");
            }

            return result;
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"HXPower工步命令发送异常: 模块{moduleNumber}通道{channelNumber}, 工步={stepType}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 将AgStepTypeEnum转换为HXPowerWorkMode
    /// </summary>
    /// <param name="stepType">工步类型</param>
    /// <returns>HXPower工作模式</returns>
    private static HXPowerDataProcessor.HXPowerWorkMode ConvertAgStepTypeToHXPowerMode(AgStepTypeEnum stepType)
    {
        return stepType switch
        {
            AgStepTypeEnum.ST => HXPowerDataProcessor.HXPowerWorkMode.Rest,
            AgStepTypeEnum.CC => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent,
            AgStepTypeEnum.DC => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent, // 放电用负值
            AgStepTypeEnum.CV => HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage,
            AgStepTypeEnum.DV => HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage, // 放电用负值
            AgStepTypeEnum.CP => HXPowerDataProcessor.HXPowerWorkMode.ConstantPower,
            AgStepTypeEnum.DP => HXPowerDataProcessor.HXPowerWorkMode.ConstantPower,   // 放电用负值
            AgStepTypeEnum.CR => HXPowerDataProcessor.HXPowerWorkMode.ConstantResistance,
            AgStepTypeEnum.DR => HXPowerDataProcessor.HXPowerWorkMode.ConstantResistance, // 放电用负值
            AgStepTypeEnum.CCCV => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrentVoltage,
            AgStepTypeEnum.DCDV => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrentVoltage, // 放电用负值
            AgStepTypeEnum.End => HXPowerDataProcessor.HXPowerWorkMode.Standby,
            _ => HXPowerDataProcessor.HXPowerWorkMode.Standby
        };
    }

    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="key">键名</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>参数值</returns>
    private static double GetParameterValue(Dictionary<string, object>? parameters, string key, double defaultValue)
    {
        if (parameters?.TryGetValue(key, out var value) == true)
        {
            if (value is double doubleValue)
                return doubleValue;

            if (double.TryParse(value?.ToString(), out var parsedValue))
                return parsedValue;
        }

        return defaultValue;
    }

    /// <summary>
    /// 重写InitChannelAsync以添加CAN发送管理器
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected internal override async Task InitChannelAsync(IChannel? channel, CancellationToken cancellationToken)
    {
        await base.InitChannelAsync(channel, cancellationToken).ConfigureAwait(false);

        // 如果是CAN通道，添加发送管理器事件处理
        if (channel is CanChannel canChannel)
        {
            // 订阅Channel事件以管理发送器
            canChannel.Started.Add(OnCanChannelStarted);
            canChannel.Stoped.Add(OnCanChannelStopped);
        }
    }

    /// <summary>
    /// CAN通道连接成功事件处理
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="last">是否为最后一个处理器</param>
    /// <returns></returns>
    private async ValueTask<bool> OnCanChannelStarted(IClientChannel channel, bool last)
    {
        try
        {
            var canDeviceRuntime = GetCanDeviceRuntime();
            if (canDeviceRuntime != null)
            {
                // 启动CAN发送管理器
                canDeviceRuntime.StartSendManager(this);
                LogMessage?.LogInformation($"HXPower CAN发送管理器已启动: {DeviceName}");
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"启动HXPower CAN发送管理器失败: {DeviceName}");
        }

        return true;
    }

    /// <summary>
    /// CAN通道断开连接事件处理
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="last">是否为最后一个处理器</param>
    /// <returns></returns>
    private async ValueTask<bool> OnCanChannelStopped(IClientChannel channel, bool last)
    {
        try
        {
            var canDeviceRuntime = GetCanDeviceRuntime();
            if (canDeviceRuntime != null)
            {
                // 停止CAN发送管理器
                canDeviceRuntime.StopSendManager();
                LogMessage?.LogInformation($"HXPower CAN发送管理器已停止: {DeviceName}");
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"停止HXPower CAN发送管理器失败: {DeviceName}");
        }

        return true;
    }
}

/// <summary>
/// HXPower设备实现
/// </summary>
public class HXPowerDevice : DeviceBase
{
    private readonly ILogger? _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public HXPowerDevice()
    {
        // 可以通过依赖注入获取日志
        _logger = null;
    }

    /// <summary>
    /// 获取地址描述
    /// </summary>
    /// <returns></returns>
    public override string GetAddressDescription()
    {
        return "HXPower地址格式: ModuleNumber.ChannelNumber (例如: 1.1 表示模块1通道1)";
    }

    /// <summary>
    /// 不支持多设备
    /// </summary>
    /// <returns></returns>
    public override bool SupportMultipleDevice()
    {
        return false;
    }

    /// <summary>
    /// 获取数据适配器
    /// </summary>
    /// <returns></returns>
    public override DataHandlingAdapter GetDataAdapter()
    {
        // HXPower使用简单的数据适配器
        return new DeviceSingleStreamDataHandleAdapter<HXPowerMessage>()
        {
            CacheTimeout = TimeSpan.FromMilliseconds(3000),
        };
    }

    /// <summary>
    /// 写入数据（核心方法）
    /// </summary>
    /// <param name="address">地址（格式：ModuleNumber.ChannelNumber）</param>
    /// <param name="value">值（包含工作模式、电流、电压、功率等参数）</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>写入结果</returns>
    public override async ValueTask<OperResult> WriteAsync(string address, JToken value, DataTypeEnum dataType, CancellationToken cancellationToken = default)
    {
        try
        {
            // 解析地址：ModuleNumber.ChannelNumber
            var parts = address.Split('.');
            if (parts.Length != 2 || !int.TryParse(parts[0], out int moduleNumber) || !int.TryParse(parts[1], out int channelNumber))
            {
                return new OperResult($"地址格式错误，应为 ModuleNumber.ChannelNumber，实际：{address}");
            }

            // 解析写入值
            var parameters = ParseWriteValue(value);
            
            // 获取工作模式
            var workMode = GetWorkMode(parameters);
            
            // 获取参数值
            var current = GetParameter<double>(parameters, "Current", 0.0);
            var voltage = GetParameter<double>(parameters, "Voltage", 0.0);
            var power = GetParameter<double>(parameters, "Power", 0.0);

            // 生成控制命令数据
            var commandData = HXPowerDataProcessor.GenerateControlCommandData(
                moduleNumber, channelNumber, workMode, current, voltage, power);

            // 计算CAN ID
            var canId = HXPowerDataProcessor.GetControlCanId(moduleNumber, channelNumber);

            // 创建CAN帧
            var canFrame = new CanFrame(canId, commandData)
            {
                IsExtended = true,
                IsRemote = false
            };

            // 发送CAN帧
            if (Channel is CanChannel canChannel)
            {
                var result = await canChannel.SendCanFrameAsync(canId, commandData);
                if (result)
                {
                    _logger?.LogDebug($"HXPower控制命令发送成功: 模块{moduleNumber}通道{channelNumber}, 模式={workMode}, CAN ID=0x{canId:X8}");
                    return OperResult.Success;
                }
                else
                {
                    return new OperResult("CAN帧发送失败");
                }
            }
            else
            {
                return new OperResult($"通道类型不支持，期望CanChannel，实际：{Channel?.GetType().Name}");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"HXPower写入失败: Address={address}, Value={value}");
            return new OperResult(ex);
        }
    }

    /// <summary>
    /// 读取数据（HXPower主要用于写入，读取功能暂不实现）
    /// </summary>
    /// <param name="address">地址</param>
    /// <param name="length">长度</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取结果</returns>
    public override async ValueTask<OperResult<byte[]>> ReadAsync(string address, int length, CancellationToken cancellationToken = default)
    {
        // HXPower主要用于发送控制命令，暂不实现读取功能
        await Task.CompletedTask;
        return new OperResult<byte[]>("HXPower设备不支持读取操作");
    }

    /// <summary>
    /// 解析写入值
    /// </summary>
    /// <param name="value">JToken值</param>
    /// <returns>参数字典</returns>
    private Dictionary<string, object> ParseWriteValue(JToken value)
    {
        var parameters = new Dictionary<string, object>();

        if (value is JObject jObject)
        {
            foreach (var property in jObject.Properties())
            {
                parameters[property.Name] = property.Value?.ToObject<object>() ?? "";
            }
        }
        else if (value is JValue jValue)
        {
            // 如果是简单值，尝试解析为模式
            if (jValue.Value is long longValue)
            {
                parameters["Mode"] = (int)longValue;
            }
            else if (jValue.Value is int intValue)
            {
                parameters["Mode"] = intValue;
            }
            else if (jValue.Value is string stringValue)
            {
                parameters["Mode"] = stringValue;
            }
        }

        return parameters;
    }

    /// <summary>
    /// 获取工作模式
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <returns>工作模式</returns>
    private HXPowerDataProcessor.HXPowerWorkMode GetWorkMode(Dictionary<string, object> parameters)
    {
        if (parameters.TryGetValue("Mode", out var modeValue))
        {
            if (modeValue is int intMode)
            {
                return intMode switch
                {
                    1 => HXPowerDataProcessor.HXPowerWorkMode.Standby,
                    2 => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent,
                    3 => HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage,
                    4 => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent, // 放电模式
                    5 => HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage, // 放电模式
                    6 => HXPowerDataProcessor.HXPowerWorkMode.ConstantPower,
                    7 => HXPowerDataProcessor.HXPowerWorkMode.ConstantPower,   // 放电模式
                    _ => HXPowerDataProcessor.HXPowerWorkMode.Standby
                };
            }
            else if (modeValue is string stringMode)
            {
                return stringMode.ToUpper() switch
                {
                    "ST" => HXPowerDataProcessor.HXPowerWorkMode.Rest,
                    "CC" => HXPowerDataProcessor.HXPowerWorkMode.ConstantCurrent,
                    "CV" => HXPowerDataProcessor.HXPowerWorkMode.ConstantVoltage,
                    "CP" => HXPowerDataProcessor.HXPowerWorkMode.ConstantPower,
                    "CR" => HXPowerDataProcessor.HXPowerWorkMode.ConstantResistance,
                    _ => HXPowerDataProcessor.HXPowerWorkMode.Standby
                };
            }
        }

        return HXPowerDataProcessor.HXPowerWorkMode.Standby;
    }

    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="parameters">参数字典</param>
    /// <param name="key">键名</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>参数值</returns>
    private T GetParameter<T>(Dictionary<string, object> parameters, string key, T defaultValue)
    {
        if (parameters.TryGetValue(key, out var value))
        {
            if (value is T directValue)
                return directValue;
            
            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                // 转换失败，返回默认值
            }
        }
        
        return defaultValue;
    }

    public override List<T> LoadSourceRead<T>(IEnumerable<IVariable> deviceVariables, int maxPack, string defaultIntervalTime)
    {
        throw new NotImplementedException();
    }

    public override ValueTask<OperResult> WriteAsync(string address, byte[] value, DataTypeEnum dataType, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public override ValueTask<OperResult> WriteAsync(string address, bool[] value, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// HXPower消息（用于数据适配器）
/// </summary>
public class HXPowerMessage : MessageBase
{
    /// <summary>
    /// CAN帧
    /// </summary>
    public CanFrame? CanFrame { get; set; }

    /// <summary>
    /// 检查头部
    /// </summary>
    /// <param name="byteBlock">字节块</param>
    /// <returns>是否有效</returns>
    public override bool CheckHead<TByteBlock>(ref TByteBlock byteBlock)
    {
        // 简单实现，始终返回true
        return true;
    }

    /// <summary>
    /// 检查主体
    /// </summary>
    /// <param name="byteBlock">字节块</param>
    /// <returns>过滤结果</returns>
    public override FilterResult CheckBody<TByteBlock>(ref TByteBlock byteBlock)
    {
        // 简单实现，返回继续处理
        return FilterResult.GoOn;
    }
}

/// <summary>
/// HXPower采集属性
/// </summary>
public class HXPowerCollectProperty : CollectFoundationPropertyBase
{
    /// <summary>
    /// 模块数量
    /// </summary>
    [DynamicProperty]
    public int ModuleCount { get; set; } = 10;

    /// <summary>
    /// 每个模块的通道数
    /// </summary>
    [DynamicProperty]
    public int ChannelPerModule { get; set; } = 2;

    /// <summary>
    /// 默认电流值
    /// </summary>
    [DynamicProperty]
    public double DefaultCurrent { get; set; } = 0.0;

    /// <summary>
    /// 默认电压值
    /// </summary>
    [DynamicProperty]
    public double DefaultVoltage { get; set; } = 0.0;

    /// <summary>
    /// 默认功率值
    /// </summary>
    [DynamicProperty]
    public double DefaultPower { get; set; } = 0.0;
} 
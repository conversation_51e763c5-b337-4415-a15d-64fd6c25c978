using Matrix.Foundation;
using Matrix.NewLife;
using Matrix.NewLife.Threading;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN采集驱动（新架构，无需变量打包）
/// </summary>
public class CanCollectDriver : CollectBase
{
    /// <summary>
    /// CAN设备运行时
    /// </summary>
    private CanDeviceRuntime? _canDeviceRuntime;

    /// <summary>
    /// CAN通道
    /// </summary>
    private CanChannel? _canChannel;

    /// <summary>
    /// 设备属性
    /// </summary>
    public override CollectPropertyBase CollectProperties => new CanCollectProperty();

    /// <summary>
    /// 初始化通道
    /// </summary>
    /// <param name="channel">通道</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected internal override async Task InitChannelAsync(IChannel? channel, CancellationToken cancellationToken)
    {
        await base.InitChannelAsync(channel, cancellationToken).ConfigureAwait(false);

        if (channel is CanChannel canChannel)
        {
            _canChannel = canChannel;
            
            // 初始化CAN设备运行时
            await InitializeCanDeviceRuntimeAsync().ConfigureAwait(false);
            
            // 注册CAN帧处理器
            _canChannel.RegisterCanFrameProcessor(ProcessCanFrame);
            
            LogMessage?.LogInformation($"CAN设备 {DeviceName} 初始化完成");
        }
        else
        {
            throw new InvalidOperationException("CAN采集驱动只能用于CAN通道");
        }
    }

    /// <summary>
    /// 初始化CAN设备运行时
    /// </summary>
    /// <returns></returns>
    private async Task InitializeCanDeviceRuntimeAsync()
    {
        try
        {
            // 创建CAN设备运行时
            _canDeviceRuntime = new CanDeviceRuntime(CurrentDevice.Id, DeviceName);

            // 获取服务
            var canBmsService = App.GetService<ICanBmsService>();
            var canMessageService = App.GetService<ICanMessageService>();
            var canSignalService = App.GetService<ICanSignalService>();

            if (canBmsService == null || canMessageService == null || canSignalService == null)
            {
                throw new InvalidOperationException("无法获取CAN相关服务");
            }

            // 获取Channel关联的CAN BMS配置
            var canBmsList = new List<CanBms>();
            if (_canChannel?.ChannelOptions.CanBmsId != null)
            {
                var canBms = await canBmsService.GetCanBmsByIdAsync(_canChannel.ChannelOptions.CanBmsId.Value).ConfigureAwait(false);
                if (canBms != null)
                {
                    canBmsList.Add(canBms);
                }
            }

            // 获取CAN发送报文服务
            var canSendMessageService = App.GetService<ICanSendMessageService>();

            // 初始化运行时
            await _canDeviceRuntime.InitializeAsync(canBmsList, canMessageService, canSignalService, canSendMessageService).ConfigureAwait(false);

            LogMessage?.LogInformation($"CAN设备运行时初始化完成，BMS数量: {canBmsList.Count}");
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"初始化CAN设备运行时失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// CAN不需要变量打包，返回空列表
    /// </summary>
    /// <param name="deviceVariables">设备变量</param>
    /// <returns>空列表</returns>
    protected override Task<List<VariableSourceRead>> ProtectedLoadSourceReadAsync(List<VariableRuntime> deviceVariables)
    {
        // CAN通讯不需要变量打包，直接返回空列表
        return Task.FromResult(new List<VariableSourceRead>());
    }

    /// <summary>
    /// 重写ExecuteAsync方法，确保正确处理CAN设备的连接状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    internal override async ValueTask<ThreadRunReturnTypeEnum> ExecuteAsync(CancellationToken cancellationToken)
    {
        LogMessage?.LogWarning($"[CAN ExecuteAsync] 开始执行，设备={CurrentDevice.Name}, 当前状态={CurrentDevice.DeviceStatus}");

        try
        {
            // 如果取消操作被请求，则返回中断状态
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 如果标志为停止，则暂停执行
            if (Pause)
            {
                // 暂停
                return ThreadRunReturnTypeEnum.Continue;
            }

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 获取设备连接状态并更新设备活动时间（像BusinessBase一样处理）
            var isConnected = IsConnected();
            var currentStatus = CurrentDevice.DeviceStatus;
            LogMessage?.LogWarning($"[CAN状态检查] IsConnected={isConnected}, 当前设备状态={currentStatus}, 设备名称={CurrentDevice.Name}");

            if (isConnected)
            {
                LogMessage?.LogWarning($"[CAN状态设置] 准备设置为在线状态，当前状态={CurrentDevice.DeviceStatus}");
                CurrentDevice.SetDeviceStatus(TimerX.Now, false);
                LogMessage?.LogWarning($"[CAN状态设置] 已设置为在线状态，新状态={CurrentDevice.DeviceStatus}");
            }
            else
            {
                LogMessage?.LogWarning($"[CAN状态设置] 准备设置为离线状态，当前状态={CurrentDevice.DeviceStatus}");
                CurrentDevice.SetDeviceStatus(TimerX.Now, true, "CAN通讯超时");
                LogMessage?.LogWarning($"[CAN状态设置] 已设置为离线状态，新状态={CurrentDevice.DeviceStatus}");
            }

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 执行CAN特定的逻辑
            await ProtectedExecuteAsync(cancellationToken).ConfigureAwait(false);

            // 再次检查取消操作是否被请求
            if (cancellationToken.IsCancellationRequested)
            {
                return ThreadRunReturnTypeEnum.Break;
            }

            // 正常返回None状态
            return ThreadRunReturnTypeEnum.None;
        }
        catch (OperationCanceledException)
        {
            return ThreadRunReturnTypeEnum.Break;
        }
        catch (ObjectDisposedException)
        {
            return ThreadRunReturnTypeEnum.Break;
        }
        catch (Exception ex)
        {
            if (cancellationToken.IsCancellationRequested)
                return ThreadRunReturnTypeEnum.Break;
            // 记录异常信息，并更新设备状态为异常
            LogMessage?.LogError(ex, "Execute");
            CurrentDevice.SetDeviceStatus(TimerX.Now, true, ex.Message);
            return ThreadRunReturnTypeEnum.None;
        }
    }

    /// <summary>
    /// CAN通讯是被动接收，不需要主动执行
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected override async ValueTask ProtectedExecuteAsync(CancellationToken cancellationToken)
    {
        // 处理脚本变量（如果有的话）
        ScriptVariableRun(cancellationToken);

        await Task.CompletedTask.ConfigureAwait(false);
    }

    /// <summary>
    /// CAN通讯不需要主动读取
    /// </summary>
    /// <param name="variableSourceRead">变量源读取</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    protected override ValueTask<OperResult<byte[]>> ReadSourceAsync(VariableSourceRead variableSourceRead, CancellationToken cancellationToken)
    {
        // CAN通讯不需要主动读取，返回空结果
        return ValueTask.FromResult(OperResult.CreateSuccessResult(Array.Empty<byte>()));
    }

    /// <summary>
    /// 处理CAN帧
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>是否处理成功</returns>
    private bool ProcessCanFrame(uint canId, ReadOnlySpan<byte> canData, DateTime timestamp)
    {
        try
        {
            if (_canDeviceRuntime != null)
            {
                var processed = _canDeviceRuntime.ProcessCanFrame(canId, canData.ToArray(), timestamp);

                if (processed)
                {
                    LogMessage?.LogTrace($"处理CAN帧成功: ID=0x{canId:X8}, 数据长度={canData.Length}, 时间戳={timestamp:HH:mm:ss.fff}");
                    LogMessage?.LogTrace($"CAN数据处理后，设备状态={CurrentDevice.DeviceStatus}, CanDeviceRuntime状态={_canDeviceRuntime?.DeviceStatus}");
                    // 注意：不在这里调用SetDeviceStatus，让CollectBase的ExecuteAsync根据IsConnected结果自动处理
                }
                else
                {
                    LogMessage?.LogTrace($"未找到匹配的BMS: ID=0x{canId:X8}");
                }

                return processed;
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"处理CAN帧失败: ID=0x{canId:X8}, 错误={ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 发送CAN数据
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">数据</param>
    /// <returns>发送结果</returns>
    public async Task<bool> SendCanDataAsync(uint canId, byte[] data)
    {
        try
        {
            if (_canChannel != null)
            {
                // 这里需要实现CAN发送逻辑
                // 具体实现取决于CanChannel的发送接口
                LogMessage?.LogInformation($"发送CAN数据: ID=0x{canId:X8}, 数据长度={data.Length}");
                return true;
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"发送CAN数据失败: ID=0x{canId:X8}, 错误={ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 根据信号值构建并发送CAN数据
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值</param>
    /// <returns>发送结果</returns>
    public async Task<bool> SendCanSignalDataAsync(uint canId, Dictionary<string, double> signalValues)
    {
        try
        {
            if (_canDeviceRuntime != null)
            {
                var data = _canDeviceRuntime.BuildSendData(canId, signalValues);
                if (data != null)
                {
                    return await SendCanDataAsync(canId, data);
                }
            }
        }
        catch (Exception ex)
        {
            LogMessage?.LogError(ex, $"发送CAN信号数据失败: ID=0x{canId:X8}, 错误={ex.Message}");
        }

        return false;
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public CanDeviceStatistics? GetStatistics()
    {
        return _canDeviceRuntime?.GetStatistics();
    }

    /// <summary>
    /// 获取所有变量值
    /// </summary>
    /// <returns>变量值字典</returns>
    public Dictionary<string, double> GetAllVariableValues()
    {
        return _canDeviceRuntime?.GetAllVariableValues() ?? new Dictionary<string, double>();
    }

    /// <summary>
    /// 检查连接状态
    /// </summary>
    /// <returns>是否连接</returns>
    public override bool IsConnected()
    {
        // CAN设备的连接状态基于实际的CAN数据接收时间
        if (_canDeviceRuntime != null)
        {
            var now = DateTime.Now;
            var timeout = TimeSpan.FromSeconds(3); // 3秒超时
            var timeSinceLastComm = now - _canDeviceRuntime.LastCommunicationTime;
            var isConnected = timeSinceLastComm <= timeout;

            // 添加调试日志
            LogMessage?.LogWarning($"[CAN连接检查] 当前时间={now:HH:mm:ss.fff}, 最后通讯时间={_canDeviceRuntime.LastCommunicationTime:HH:mm:ss.fff}, 间隔={timeSinceLastComm.TotalSeconds:F1}秒, 连接状态={isConnected}");

            // 直接在这里设置状态，看看是否生效
            if (isConnected)
            {
                if (CurrentDevice.DeviceStatus != DeviceStatusEnum.OnLine)
                {
                    LogMessage?.LogWarning($"[CAN连接检查] 直接设置为在线状态");
                    CurrentDevice.DeviceStatus = DeviceStatusEnum.OnLine;
                }
            }
            else
            {
                if (CurrentDevice.DeviceStatus != DeviceStatusEnum.OffLine)
                {
                    LogMessage?.LogWarning($"[CAN连接检查] 直接设置为离线状态");
                    CurrentDevice.DeviceStatus = DeviceStatusEnum.OffLine;
                }
            }

            return isConnected;
        }

        // 如果没有CAN设备运行时，则检查通道状态
        var channelOnline = _canChannel?.Online ?? false;
        LogMessage?.LogTrace($"CAN设备连接检查: 无CAN设备运行时，通道状态={channelOnline}");
        return channelOnline;
    }

    /// <summary>
    /// 资源清理
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            try
            {
                // 注销CAN帧处理器
                if (_canChannel != null)
                {
                    _canChannel.UnregisterCanFrameProcessor(ProcessCanFrame);
                }
            }
            catch (Exception ex)
            {
                LogMessage?.LogWarning($"清理CAN采集驱动资源时发生错误: {ex.Message}");
            }
        }

        base.Dispose(disposing);
    }
}

/// <summary>
/// CAN采集属性
/// </summary>
public class CanCollectProperty : CollectPropertyBase
{
    /// <summary>
    /// CAN设备不需要重试，因为是被动接收
    /// </summary>
    public override int RetryCount { get; set; } = 0;

    /// <summary>
    /// CAN设备不需要并发读取
    /// </summary>
    public override int MaxConcurrentCount { get; set; } = 1;

    /// <summary>
    /// CAN设备重连间隔时间
    /// </summary>
    public override int ReIntervalTime { get; set; } = 5000;

}

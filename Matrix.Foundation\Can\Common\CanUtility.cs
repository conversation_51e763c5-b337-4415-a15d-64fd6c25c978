using System;
using System.Linq;

namespace Matrix.Foundation;

/// <summary>
/// CAN工具类
/// </summary>
public static class CanUtility
{
    /// <summary>
    /// CAN协议常量
    /// </summary>
    public static readonly Protocol Can = new Protocol("can");

    /// <summary>
    /// 标准帧最大ID
    /// </summary>
    public const uint StandardFrameMaxId = 0x7FF;

    /// <summary>
    /// 扩展帧最大ID
    /// </summary>
    public const uint ExtendedFrameMaxId = 0x1FFFFFFF;

    /// <summary>
    /// 标准CAN最大数据长度
    /// </summary>
    public const int StandardCanMaxDataLength = 8;

    /// <summary>
    /// CAN FD最大数据长度
    /// </summary>
    public const int CanFdMaxDataLength = 64;

    /// <summary>
    /// 判断是否为标准帧
    /// </summary>
    /// <param name="id">CAN ID</param>
    /// <returns></returns>
    public static bool IsStandardFrame(uint id)
    {
        return id <= StandardFrameMaxId;
    }

    /// <summary>
    /// 判断是否为扩展帧
    /// </summary>
    /// <param name="id">CAN ID</param>
    /// <returns></returns>
    public static bool IsExtendedFrame(uint id)
    {
        return id > StandardFrameMaxId && id <= ExtendedFrameMaxId;
    }

    /// <summary>
    /// 验证CAN ID的有效性
    /// </summary>
    /// <param name="id">CAN ID</param>
    /// <param name="isExtended">是否为扩展帧</param>
    /// <returns></returns>
    public static bool IsValidCanId(uint id, bool isExtended)
    {
        if (isExtended)
        {
            return id <= ExtendedFrameMaxId;
        }
        else
        {
            return id <= StandardFrameMaxId;
        }
    }

    /// <summary>
    /// 验证数据长度的有效性
    /// </summary>
    /// <param name="dataLength">数据长度</param>
    /// <param name="isCanFd">是否为CAN FD</param>
    /// <returns></returns>
    public static bool IsValidDataLength(int dataLength, bool isCanFd)
    {
        if (dataLength < 0)
        {
            return false;
        }

        if (isCanFd)
        {
            return dataLength <= CanFdMaxDataLength;
        }
        else
        {
            return dataLength <= StandardCanMaxDataLength;
        }
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <param name="separator">分隔符</param>
    /// <returns></returns>
    public static byte[] HexStringToBytes(string hexString, string separator = " ")
    {
        if (string.IsNullOrWhiteSpace(hexString))
        {
            return Array.Empty<byte>();
        }

        try
        {
            // 移除分隔符
            var cleanHex = hexString.Replace(separator, "").Replace(" ", "");
            
            // 确保长度为偶数
            if (cleanHex.Length % 2 != 0)
            {
                cleanHex = "0" + cleanHex;
            }

            var bytes = new byte[cleanHex.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
            }

            return bytes;
        }
        catch
        {
            return Array.Empty<byte>();
        }
    }

    /// <summary>
    /// 创建标准CAN帧
    /// </summary>
    /// <param name="id">CAN ID (0x000-0x7FF)</param>
    /// <param name="data">数据</param>
    /// <returns></returns>
    public static CanFrame CreateStandardFrame(uint id, byte[] data)
    {
        if (!IsValidCanId(id, false))
        {
            throw new ArgumentException($"Invalid standard frame ID: 0x{id:X}. Must be <= 0x{StandardFrameMaxId:X}");
        }

        return new CanFrame(id, data)
        {
            IsExtended = false
        };
    }

    /// <summary>
    /// 创建扩展CAN帧
    /// </summary>
    /// <param name="id">CAN ID (0x00000000-0x1FFFFFFF)</param>
    /// <param name="data">数据</param>
    /// <returns></returns>
    public static CanFrame CreateExtendedFrame(uint id, byte[] data)
    {
        if (!IsValidCanId(id, true))
        {
            throw new ArgumentException($"Invalid extended frame ID: 0x{id:X}. Must be <= 0x{ExtendedFrameMaxId:X}");
        }

        return new CanFrame(id, data)
        {
            IsExtended = true
        };
    }

    /// <summary>
    /// 创建CAN FD帧
    /// </summary>
    /// <param name="id">CAN ID</param>
    /// <param name="data">数据（最大64字节）</param>
    /// <param name="isExtended">是否为扩展帧</param>
    /// <returns></returns>
    public static CanFrame CreateCanFdFrame(uint id, byte[] data, bool isExtended = false)
    {
        if (!IsValidCanId(id, isExtended))
        {
            throw new ArgumentException($"Invalid CAN ID: 0x{id:X}");
        }

        if (!IsValidDataLength(data?.Length ?? 0, true))
        {
            throw new ArgumentException($"Invalid CAN FD data length: {data?.Length ?? 0}. Must be <= {CanFdMaxDataLength}");
        }

        return new CanFrame(id, data)
        {
            IsExtended = isExtended,
            IsCanFd = true
        };
    }

    /// <summary>
    /// 解析以太网CAN数据包
    /// 正确格式：帧信息(1字节) + CAN_ID(4字节) + 数据(0-8字节)
    /// 示例：FF 22 F4 30 75 F4 7E 00 00 00 00 00 00
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <returns></returns>
    public static CanFrame ParseEthernetCanPacket(byte[] packet)
    {
        if (packet == null || packet.Length < 5)
        {
            throw new ArgumentException("Invalid ethernet CAN packet: too short");
        }

        // 第1字节：帧信息（包含扩展帧等信息）
        var frameInfo = packet[0];

        // 解析帧信息
        bool isExtended = (frameInfo & 0x80) != 0; // 假设最高位表示扩展帧
        bool isRemote = (frameInfo & 0x40) != 0;   // 假设次高位表示远程帧

        // 第2-5字节：CAN ID (4字节，大端序)
        var canId = (uint)((packet[1] << 24) | (packet[2] << 16) | (packet[3] << 8) | packet[4]);

        // 第6字节及之后：数据
        var dataLength = packet.Length - 5;
        var canData = new byte[Math.Min(dataLength, 8)]; // CAN数据最多8字节

        if (dataLength > 0)
        {
            Array.Copy(packet, 5, canData, 0, canData.Length);
        }

        var frame = new CanFrame(canId, canData)
        {
            IsExtended = isExtended,
            IsRemote = isRemote
        };

        return frame;
    }

    /// <summary>
    /// 构建以太网CAN数据包
    /// 格式：帧信息(1字节) + CAN_ID(4字节) + 数据(8字节，不足补0)
    /// 固定长度：13字节
    /// </summary>
    /// <param name="frame">CAN帧</param>
    /// <returns></returns>
    public static byte[] BuildEthernetCanPacket(CanFrame frame)
    {
        if (frame == null)
        {
            throw new ArgumentNullException(nameof(frame));
        }

        var packet = new byte[13]; // 固定13字节：1字节帧信息 + 4字节ID + 8字节数据

        // 第1字节：帧信息
        byte frameInfo = 0x00;
        if (frame.IsExtended)
            frameInfo |= 0x80; // 最高位表示扩展帧
        if (frame.IsRemote)
            frameInfo |= 0x40; // 次高位表示远程帧

        packet[0] = frameInfo;

        // 第2-5字节：CAN ID（大端序）
        packet[1] = (byte)((frame.Id >> 24) & 0xFF);
        packet[2] = (byte)((frame.Id >> 16) & 0xFF);
        packet[3] = (byte)((frame.Id >> 8) & 0xFF);
        packet[4] = (byte)(frame.Id & 0xFF);

        // 第6-13字节：数据（8字节，不足补0）
        var dataLength = Math.Min(frame.Data?.Length ?? 0, 8);
        if (dataLength > 0)
        {
            Array.Copy(frame.Data, 0, packet, 5, dataLength);
        }
        // 剩余字节自动为0

        return packet;
    }
}

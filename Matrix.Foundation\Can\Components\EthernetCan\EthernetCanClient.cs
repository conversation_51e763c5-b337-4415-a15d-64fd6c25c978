using System;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;

namespace Matrix.Foundation;

/// <summary>
/// 以太网CAN客户端
/// 基于TCP连接，数据格式：ID(4字节) + Data(0-8字节)
/// </summary>
public class EthernetCanClient : CanClientBase, ICanClient
{
    #region 字段

    private TcpClient m_tcpClient;
    private IPHost m_remoteIPHost;

    #endregion 字段

    #region 事件

    /// <inheritdoc/>
    public ConnectedEventHandler<ICanClient> Connected { get; set; }

    /// <inheritdoc/>
    public ConnectingEventHandler<ICanClient> Connecting { get; set; }

    /// <inheritdoc/>
    public ClosedEventHandler<ICanClient> Closed { get; set; }

    /// <inheritdoc/>
    public ClosingEventHandler<ICanClient> Closing { get; set; }

    /// <inheritdoc/>
    public CanReceivedEventHandler<ICanClient> Received { get; set; }

    #endregion 事件

    #region 属性



    /// <summary>
    /// 底层TCP客户端
    /// </summary>
    public TcpClient TcpClient => this.m_tcpClient;

    #endregion 属性

    #region 连接操作

    /// <inheritdoc/>
    protected override async Task ProtectedConnectAsync(int millisecondsTimeout, CancellationToken token)
    {
        try
        {
            // 解析配置信息
            var configInfo = this.CanConfigInfo;
            if (configInfo == null || string.IsNullOrEmpty(configInfo.IpAddress))
            {
                throw new ArgumentException($"无效的以太网CAN配置: {this.CanSet}");
            }

            // 解析IP地址和端口
            try
            {
                this.m_remoteIPHost = new IPHost($"{configInfo.IpAddress}:{configInfo.Port}");
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"无效的IP地址格式: {configInfo.IpAddress}:{configInfo.Port}", ex);
            }

            // 创建TCP客户端
            this.m_tcpClient = new TcpClient();
            
            // 配置TCP客户端
            await this.m_tcpClient.SetupAsync(new TouchSocketConfig()
                .SetRemoteIPHost(this.m_remoteIPHost)
                .SetTcpDataHandlingAdapter(() => new EthernetCanDataAdapter())
                .ConfigureContainer(a =>
                {
                    if (this.Resolver != null)
                    {
                        a.RegisterSingleton<IResolver>(this.Resolver);
                    }
                })).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            // 设置TCP客户端事件
            this.m_tcpClient.Connected += this.OnTcpConnected;
            this.m_tcpClient.Closed += this.OnTcpClosed;
            this.m_tcpClient.Received += this.OnTcpReceived;

            // 连接TCP服务器
            await this.m_tcpClient.ConnectAsync(millisecondsTimeout, token).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            this.Logger?.Info($"成功连接到以太网CAN服务器: {this.m_remoteIPHost}");
        }
        catch (Exception ex)
        {
            throw new Exception($"连接以太网CAN失败: {ex.Message}", ex);
        }
    }

    #endregion 连接操作

    #region 断开操作

    /// <inheritdoc/>
    protected override async Task ProtectedCloseAsync(string msg)
    {
        try
        {
            if (this.m_tcpClient != null)
            {
                // 移除事件处理
                this.m_tcpClient.Connected -= this.OnTcpConnected;
                this.m_tcpClient.Closed -= this.OnTcpClosed;
                this.m_tcpClient.Received -= this.OnTcpReceived;

                // 关闭TCP连接
                await this.m_tcpClient.CloseAsync(msg).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
                this.m_tcpClient.SafeDispose();
                this.m_tcpClient = null;
            }

            this.Logger?.Info($"以太网CAN连接已关闭: {msg}");
        }
        catch (Exception ex)
        {
            this.Logger?.Error(this, $"关闭以太网CAN连接时发生错误: {ex.Message}");
        }
    }

    #endregion 断开操作

    #region 发送操作

    /// <inheritdoc/>
    protected override async Task ProtectedSendAsync(CanFrame frame)
    {
        this.ThrowIfNotConnected();

        try
        {
            // 触发发送前事件
            var sendingArgs = new CanSendingEventArgs(frame);
            if (this.PluginManager.Enable)
            {
                await this.PluginManager.RaiseAsync(typeof(ICanSendingPlugin), this.Resolver, this, sendingArgs).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
            }

            // 构建以太网CAN数据包
            var packet = CanUtility.BuildEthernetCanPacket(frame);

            // 通过TCP发送
            await this.m_tcpClient.SendAsync(packet).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            this.Logger?.Debug($"发送以太网CAN帧: {frame}");
        }
        catch (Exception ex)
        {
            throw new Exception($"发送以太网CAN帧失败: {ex.Message}", ex);
        }
    }

    #endregion 发送操作

    #region TCP事件处理

    /// <summary>
    /// TCP连接成功事件
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private Task OnTcpConnected(ITcpClient client, ConnectedEventArgs e)
    {
        this.Logger?.Debug("TCP连接已建立");
        return EasyTask.CompletedTask;
    }

    /// <summary>
    /// TCP连接关闭事件
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private Task OnTcpClosed(ITcpClient client, ClosedEventArgs e)
    {
        this.Logger?.Debug($"TCP连接已关闭: {e.Message}");
        return EasyTask.CompletedTask;
    }

    /// <summary>
    /// TCP接收数据事件
    /// </summary>
    /// <param name="client">TCP客户端</param>
    /// <param name="e">事件参数</param>
    /// <returns></returns>
    private async Task OnTcpReceived(ITcpClient client, ReceivedDataEventArgs e)
    {
        try
        {
            // 解析以太网CAN数据包
            var packet = e.ByteBlock.ToArray();

            // 记录原始数据用于调试
            if (this.Logger?.LogLevel <= TouchSocket.Core.LogLevel.Trace)
            {
                var hexData = string.Join(" ", packet.Select(b => b.ToString("X2")));
                this.Logger?.Trace($"接收到以太网CAN原始数据({packet.Length}字节): {hexData}");
            }

            var frame = CanUtility.ParseEthernetCanPacket(packet);

            // 处理接收到的CAN帧
            await this.HandleReceivedFrame(frame).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            this.Logger?.Debug($"解析以太网CAN帧: ID=0x{frame.Id:X8}, 数据长度={frame.Data?.Length ?? 0}");
        }
        catch (Exception ex)
        {
            var hexData = string.Join(" ", e.ByteBlock.ToArray().Select(b => b.ToString("X2")));
            this.Logger?.Error($"解析以太网CAN数据包时发生错误: {ex.Message}, 原始数据: {hexData}");
        }
    }

    #endregion TCP事件处理

    #region 事件处理

    /// <inheritdoc/>
    protected override async Task OnCanConnected(ConnectedEventArgs e)
    {
        if (this.Connected != null)
        {
            await this.Connected.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await base.OnCanConnected(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <inheritdoc/>
    protected override async Task OnCanConnecting(ConnectingEventArgs e)
    {
        if (this.Connecting != null)
        {
            await this.Connecting.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await base.OnCanConnecting(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <inheritdoc/>
    protected override async Task OnCanClosed(ClosedEventArgs e)
    {
        if (this.Closed != null)
        {
            await this.Closed.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await base.OnCanClosed(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <inheritdoc/>
    protected override async Task OnCanClosing(ClosingEventArgs e)
    {
        if (this.Closing != null)
        {
            await this.Closing.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await base.OnCanClosing(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <inheritdoc/>
    protected override async Task OnCanReceived(CanReceivedEventArgs e)
    {
        if (this.Received != null)
        {
            await this.Received.Invoke(this, e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
        }
        await base.OnCanReceived(e).ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    #endregion 事件处理
}

/// <summary>
/// 以太网CAN数据适配器
/// </summary>
internal class EthernetCanDataAdapter : SingleStreamDataHandlingAdapter
{
    private readonly List<byte> _buffer = new List<byte>();

    /// <inheritdoc/>
    public override bool CanSplicingSend => false;

    /// <inheritdoc/>
    public override bool CanSendRequestInfo => false;

    /// <inheritdoc/>
    protected override async Task PreviewReceivedAsync(ByteBlock byteBlock)
    {
        // 将接收到的数据添加到缓冲区
        var data = byteBlock.ToArray();
        _buffer.AddRange(data);

        // 解析缓冲区中的CAN帧
        await ParseCanFramesFromBuffer().ConfigureAwait(EasyTask.ContinueOnCapturedContext);
    }

    /// <summary>
    /// 从缓冲区解析CAN帧
    /// 每个CAN帧格式：帧信息(1字节) + CAN_ID(4字节) + 数据(0-8字节)
    /// 固定长度：13字节（1+4+8）
    /// </summary>
    private async Task ParseCanFramesFromBuffer()
    {
        const int FRAME_LENGTH = 13; // 固定帧长度：1字节帧信息 + 4字节ID + 8字节数据

        var bufferArray = _buffer.ToArray();
        var offset = 0;

        while (offset + FRAME_LENGTH <= bufferArray.Length)
        {
            // 提取单个CAN帧数据（固定13字节）
            var frameData = new byte[FRAME_LENGTH];
            Array.Copy(bufferArray, offset, frameData, 0, FRAME_LENGTH);

            // 传递给上层处理
            var frameByteBlock = new ByteBlock(frameData);
            await this.GoReceivedAsync(frameByteBlock, null).ConfigureAwait(EasyTask.ContinueOnCapturedContext);

            // 移动到下一帧
            offset += FRAME_LENGTH;
        }

        // 清除已处理的数据
        if (offset > 0)
        {
            _buffer.RemoveRange(0, offset);
        }
    }


}

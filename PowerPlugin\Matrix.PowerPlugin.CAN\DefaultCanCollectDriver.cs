using Matrix.Aging.Application;
using Matrix.Foundation;
using Matrix.NewLife;
using Matrix.NewLife.Threading;
using Newtonsoft.Json.Linq;

namespace Matrix.PowerPlugin.CAN;

/// <summary>
/// 默认CAN采集驱动（当CAN设备未指定插件时使用）
/// </summary>
public class DefaultCanCollectDriver : CollectBase
{
    /// <summary>
    /// 设备属性
    /// </summary>
    public override CollectPropertyBase CollectProperties => new DefaultCanCollectProperty();

    /// <summary>
    /// 获取地址描述
    /// </summary>
    /// <returns>地址描述</returns>
    public override string GetAddressDescription()
    {
        return "默认CAN设备：基于CanBms配置自动处理CAN通讯，无需手动配置变量地址";
    }

    /// <summary>
    /// 重写ProtectedExecuteAsync方法，强制设置设备状态
    /// </summary>
    protected override async ValueTask ProtectedExecuteAsync(CancellationToken cancellationToken)
    {
        // 强制设置设备状态为在线，确保状态正确显示
        LogMessage?.LogWarning($"[DefaultCAN ProtectedExecuteAsync] 强制设置设备状态，当前状态={CurrentDevice.DeviceStatus}");

        if (CurrentDevice.DeviceStatus != DeviceStatusEnum.OnLine)
        {
            CurrentDevice.DeviceStatus = DeviceStatusEnum.OnLine;
            LogMessage?.LogWarning($"[DefaultCAN ProtectedExecuteAsync] 已强制设置为在线状态");
        }

        // 调用基类的ProtectedExecuteAsync
        await base.ProtectedExecuteAsync(cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 检查是否连接
    /// </summary>
    /// <returns>连接状态</returns>
    public override bool IsConnected()
    {
        // 默认CAN驱动简化逻辑：总是返回true
        LogMessage?.LogWarning($"[DefaultCAN IsConnected] 当前设备状态={CurrentDevice.DeviceStatus}");
        return true; // 默认返回连接状态
    }

    /// <summary>
    /// CAN不需要变量打包，返回空列表
    /// </summary>
    /// <param name="deviceVariables">设备变量</param>
    /// <returns>空列表</returns>
    protected override Task<List<VariableSourceRead>> ProtectedLoadSourceReadAsync(List<VariableRuntime> deviceVariables)
    {
        // CAN通讯不需要变量打包，直接返回空列表
        return Task.FromResult(new List<VariableSourceRead>());
    }

    /// <summary>
    /// 读取数据源（CAN模式不使用此方法）
    /// </summary>
    /// <param name="variableSourceRead">变量源读取</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    protected override async ValueTask<OperResult<byte[]>> ReadSourceAsync(VariableSourceRead variableSourceRead, CancellationToken cancellationToken)
    {
        // CAN模式采用被动接收，不使用主动读取
        LogMessage?.LogWarning("CAN设备使用被动接收模式，不支持主动读取操作");
        return new OperResult<byte[]>("CAN设备使用被动接收模式，不支持主动读取操作");
    }

    /// <summary>
    /// 批量写入变量值（CAN模式通过信号写入）
    /// </summary>
    /// <param name="writeInfoLists">写入信息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>写入结果字典</returns>
    protected override async ValueTask<Dictionary<string, OperResult>> WriteValuesAsync(Dictionary<VariableRuntime, JToken> writeInfoLists, CancellationToken cancellationToken)
    {
        var results = new Dictionary<string, OperResult>();

        foreach (var kvp in writeInfoLists)
        {
            var variableRuntime = kvp.Key;
            var value = kvp.Value;

            try
            {
                // 尝试通过CAN信号写入
                if (double.TryParse(value?.ToString(), out var doubleValue))
                {
                    // CAN信号写入现在通过插件的写方法实现
                    // 格式：Signal1=value,Signal2=value
                    var signalWriteRequest = $"{variableRuntime.Name}={doubleValue}";

                    // 这里应该通过CAN插件的写方法来实现信号写入
                    // 暂时标记为未实现
                    LogMessage?.LogWarning($"CAN信号写入功能需要通过CAN插件实现: {signalWriteRequest}");
                    results[variableRuntime.Name] = new OperResult("CAN信号写入功能需要通过CAN插件实现");
                }
                else
                {
                    results[variableRuntime.Name] = new OperResult($"CAN信号值格式错误: {value}，期望数值类型");
                }
            }
            catch (Exception ex)
            {
                LogMessage?.LogError(ex, $"CAN信号写入异常: {variableRuntime.Name}={value}");
                results[variableRuntime.Name] = new OperResult($"CAN信号写入异常: {ex.Message}");
            }
        }

        return results;
    }
}

/// <summary>
/// 默认CAN采集属性
/// </summary>
public class DefaultCanCollectProperty : CollectPropertyBase
{
    /// <summary>
    /// CAN设备不需要重试，因为是被动接收
    /// </summary>
    public override int RetryCount { get; set; } = 0;

    /// <summary>
    /// CAN设备不需要并发读取
    /// </summary>
    public override int MaxConcurrentCount { get; set; } = 1;

    /// <summary>
    /// CAN设备重连间隔时间
    /// </summary>
    public override int ReIntervalTime { get; set; } = 5000;
}

using BootstrapBlazor.Components;
using Matrix.Foundation;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN信号表
/// </summary>
[SugarTable("can_signal", TableDescription = "CAN信号表")]
[Tenant(SqlSugarConst.DB_Custom)]
[SugarIndex("index_can_message", nameof(CanSignal.CanMessageId), OrderByType.Asc)]
[SugarIndex("unique_signal_name", nameof(CanSignal.SignalName), OrderByType.Asc, nameof(CanSignal.CanMessageId), OrderByType.Asc, true)]
public class CanSignal : BaseDataEntity, IValidatableObject
{
    /// <summary>
    /// 对应的CanMessage ID（关联CanMessage表的Id）
    /// </summary>
    [SugarColumn(ColumnDescription = "CanMessage ID")]
    [AutoGenerateColumn(Visible = true, Order = 1, Filterable = false, Sortable = false)]
    [Required]
    [NotNull]
    public virtual long CanMessageId { get; set; }

    /// <summary>
    /// 信号名称
    /// </summary>
    [SugarColumn(ColumnDescription = "信号名称", Length = 100, IsNullable = false)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true, Order = 1)]
    [Required]
    public virtual string SignalName { get; set; }



    /// <summary>
    /// 解析方式（Intel或Motorola）
    /// </summary>
    [SugarColumn(ColumnDescription = "解析方式")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual CanByteOrderEnum ByteOrder { get; set; } = CanByteOrderEnum.Intel;

    /// <summary>
    /// 起始位
    /// </summary>
    [SugarColumn(ColumnDescription = "起始位")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    [Range(0, 511)]
    public virtual ushort StartBit { get; set; }

    /// <summary>
    /// 位长度
    /// </summary>
    [SugarColumn(ColumnDescription = "位长度")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    [Range(1, 64)]
    public virtual byte BitLength { get; set; } = 1;

    /// <summary>
    /// 分辨率
    /// </summary>
    [SugarColumn(ColumnDescription = "分辨率")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual double Resolution { get; set; } = 1.0;

    /// <summary>
    /// 偏移量
    /// </summary>
    [SugarColumn(ColumnDescription = "偏移量")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual double Offset { get; set; } = 0.0;

    /// <summary>
    /// 是否为有符号信号
    /// </summary>
    [SugarColumn(ColumnDescription = "是否有符号")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsSigned { get; set; } = false;

    /// <summary>
    /// 最大值
    /// </summary>
    [SugarColumn(ColumnDescription = "最大值", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual double? MaxValue { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    [SugarColumn(ColumnDescription = "最小值", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual double? MinValue { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnDescription = "单位", Length = 50, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 初始值
    /// </summary>
    [SugarColumn(ColumnDescription = "初始值", IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual double? InitialValue { get; set; }

    /// <summary>
    /// 多包设置
    /// </summary>
    [SugarColumn(ColumnDescription = "多包设置", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? MultiPacketConfig { get; set; }

    /// <summary>
    /// 是否保存（非DBC内设置）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否保存")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsSave { get; set; } = true;

    /// <summary>
    /// 是否显示（非DBC内设置）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否显示")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool IsDisplay { get; set; } = true;

    /// <summary>
    /// 信号值（运行时值，不存储到数据库）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [AutoGenerateColumn(Ignore = true)]
    public virtual object? SignalValue { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 200, IsNullable = true)]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual string? Description { get; set; }

    /// <summary>
    /// 使能
    /// </summary>
    [SugarColumn(ColumnDescription = "使能")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual bool Enable { get; set; } = true;

    /// <summary>
    /// 数据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "数据类型")]
    [AutoGenerateColumn(Visible = true, Filterable = true, Sortable = true)]
    public virtual DataTypeEnum DataType { get; set; } = DataTypeEnum.Double;



    /// <summary>
    /// 验证方法
    /// </summary>
    /// <param name="validationContext">验证上下文</param>
    /// <returns>验证结果</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // 验证起始位和位长度
        if (StartBit + BitLength > 64)
        {
            results.Add(new ValidationResult("起始位 + 位长度不能超过64位", new[] { nameof(StartBit), nameof(BitLength) }));
        }

        // 验证最大值和最小值
        if (MaxValue.HasValue && MinValue.HasValue && MaxValue.Value < MinValue.Value)
        {
            results.Add(new ValidationResult("最大值不能小于最小值", new[] { nameof(MaxValue), nameof(MinValue) }));
        }

        // 验证分辨率
        if (Resolution == 0)
        {
            results.Add(new ValidationResult("分辨率不能为0", new[] { nameof(Resolution) }));
        }

        // 验证位长度与数据类型的匹配
        var maxBits = GetMaxBitsForDataType(DataType);
        if (BitLength > maxBits)
        {
            results.Add(new ValidationResult($"数据类型 {DataType} 的最大位长度为 {maxBits}", new[] { nameof(BitLength), nameof(DataType) }));
        }

        return results;
    }

    /// <summary>
    /// 获取数据类型对应的最大位长度
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>最大位长度</returns>
    private static int GetMaxBitsForDataType(DataTypeEnum dataType)
    {
        return dataType switch
        {
            DataTypeEnum.Boolean => 1,
            DataTypeEnum.Byte => 8,
            DataTypeEnum.Int16 or DataTypeEnum.UInt16 => 16,
            DataTypeEnum.Int32 or DataTypeEnum.UInt32 or DataTypeEnum.Single => 32,
            DataTypeEnum.Int64 or DataTypeEnum.UInt64 or DataTypeEnum.Double => 64,
            _ => 64
        };
    }
}



/// <summary>
/// CAN字节序枚举
/// </summary>
public enum CanByteOrderEnum
{
    /// <summary>
    /// Intel字节序（小端）
    /// </summary>
    Intel = 0,

    /// <summary>
    /// Motorola字节序（大端）
    /// </summary>
    Motorola = 1
}

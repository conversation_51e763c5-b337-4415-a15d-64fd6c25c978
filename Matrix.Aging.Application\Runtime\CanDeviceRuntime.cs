using Matrix.Foundation;
using System.Collections.Concurrent;

namespace Matrix.Aging.Application;

/// <summary>
/// CAN设备运行时管理类
/// </summary>
public class CanDeviceRuntime : IDisposable
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; private set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; private set; }

    /// <summary>
    /// CAN ID到BMS运行时的映射（用于快速查找）
    /// </summary>
    public ConcurrentDictionary<uint, CanBmsRuntime> CanIdBmsMap { get; private set; } = new();

    /// <summary>
    /// 所有BMS运行时列表
    /// </summary>
    public List<CanBmsRuntime> BmsRuntimes { get; private set; } = new();

    /// <summary>
    /// 启用发送的BMS列表
    /// </summary>
    public List<CanBmsRuntime> SendEnabledBms { get; private set; } = new();

    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatusEnum DeviceStatus { get; set; } = DeviceStatusEnum.OffLine;

    /// <summary>
    /// 最后通讯时间
    /// </summary>
    public DateTime LastCommunicationTime { get; set; }

    /// <summary>
    /// CAN发送管理器
    /// </summary>
    public CanSendManager? SendManager { get; private set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="deviceName">设备名称</param>
    public CanDeviceRuntime(long deviceId, string deviceName)
    {
        DeviceId = deviceId;
        DeviceName = deviceName;
    }

    /// <summary>
    /// 初始化CAN BMS配置
    /// </summary>
    /// <param name="canBmsList">CAN BMS配置列表</param>
    /// <param name="canMessageService">CAN报文服务</param>
    /// <param name="canSignalService">CAN信号服务</param>
    /// <param name="canSendMessageService">CAN发送报文服务</param>
    public async Task InitializeAsync(List<CanBms> canBmsList, ICanMessageService canMessageService, ICanSignalService canSignalService, ICanSendMessageService canSendMessageService)
    {
        BmsRuntimes.Clear();
        CanIdBmsMap.Clear();
        SendEnabledBms.Clear();

        foreach (var canBms in canBmsList)
        {
            if (canBms?.Enable == true)
            {
                // 创建BMS运行时
                var bmsRuntime = new CanBmsRuntime(canBms);

                // 获取该BMS下的所有报文
                var canMessages = await canMessageService.GetCanMessagesByCanBmsIdAsync(canBms.Id).ConfigureAwait(false);

                // 初始化BMS运行时
                await bmsRuntime.InitializeAsync(canMessages, canSignalService, canSendMessageService).ConfigureAwait(false);

                // 添加到列表
                BmsRuntimes.Add(bmsRuntime);

                // 建立CAN ID映射（所有报文ID）
                foreach (var messageRuntime in bmsRuntime.MessageRuntimes.Values)
                {
                    CanIdBmsMap[messageRuntime.CanMessage.MessageId] = bmsRuntime;
                }

                // 如果有发送报文，添加到发送列表
                if (bmsRuntime.SendEnabledMessages.Count > 0)
                {
                    SendEnabledBms.Add(bmsRuntime);
                }
            }
        }
    }

    /// <summary>
    /// 启动发送管理器（在Channel连接成功后调用）
    /// </summary>
    /// <param name="canSender">CAN发送器</param>
    public void StartSendManager(ICanSender canSender)
    {
        if (SendManager != null)
        {
            SendManager.Dispose();
        }

        SendManager = new CanSendManager(canSender);

        // 启动所有BMS的后台发送任务
        foreach (var bmsRuntime in BmsRuntimes)
        {
            if (bmsRuntime.BackgroundSendMessages.Count > 0)
            {
                SendManager.StartBmsSendTasks(bmsRuntime);
            }
        }
    }

    /// <summary>
    /// 停止发送管理器（在Channel断开连接时调用）
    /// </summary>
    public void StopSendManager()
    {
        SendManager?.StopAllSendTasks();
        SendManager?.Dispose();
        SendManager = null;
    }

    /// <summary>
    /// 处理接收到的CAN帧
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="canData">CAN数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>是否处理成功</returns>
    public bool ProcessCanFrame(uint canId, byte[] canData, DateTime timestamp)
    {
        if (CanIdBmsMap.TryGetValue(canId, out var bmsRuntime))
        {
            try
            {
                var success = bmsRuntime.ProcessCanFrame(canId, canData.AsSpan(), timestamp);
                if (success)
                {
                    LastCommunicationTime = timestamp;
                    DeviceStatus = DeviceStatusEnum.OnLine;
                }
                return success;
            }
            catch (Exception)
            {
                // 解析失败
                return false;
            }
        }

        return false; // 未找到对应的BMS
    }

    /// <summary>
    /// 获取指定CAN ID的BMS运行时
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <returns>BMS运行时</returns>
    public CanBmsRuntime? GetBmsRuntime(uint canId)
    {
        return CanIdBmsMap.TryGetValue(canId, out var bmsRuntime) ? bmsRuntime : null;
    }

    /// <summary>
    /// 获取指定CAN ID的信号值
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double>? GetSignalValuesByCanId(uint canId)
    {
        var bmsRuntime = GetBmsRuntime(canId);
        var messageRuntime = bmsRuntime?.GetMessageRuntime(canId);
        return messageRuntime?.GetAllSignalValues();
    }

    /// <summary>
    /// 获取所有信号值
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetAllSignalValues()
    {
        var result = new Dictionary<string, double>();

        foreach (var bmsRuntime in BmsRuntimes)
        {
            var bmsValues = bmsRuntime.GetAllSignalValues();
            foreach (var kvp in bmsValues)
            {
                result[kvp.Key] = kvp.Value;
            }
        }

        return result;
    }

    /// <summary>
    /// 获取所有变量值（与GetAllSignalValues等效，用于兼容性）
    /// </summary>
    /// <returns>信号值字典</returns>
    public Dictionary<string, double> GetAllVariableValues()
    {
        return GetAllSignalValues();
    }

    /// <summary>
    /// 设置信号值
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>是否设置成功</returns>
    public bool SetSignalValue(string signalName, double value)
    {
        foreach (var bmsRuntime in BmsRuntimes)
        {
            if (bmsRuntime.SetSignalValue(signalName, value))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 设置信号值并构建发送数据
    /// </summary>
    /// <param name="signalName">信号名称</param>
    /// <param name="value">信号值</param>
    /// <returns>发送数据结果(CAN ID, Data)</returns>
    public (uint canId, byte[] data)? SetSignalValueAndBuildSendData(string signalName, double value)
    {
        foreach (var bmsRuntime in BmsRuntimes)
        {
            var result = bmsRuntime.SetSignalValueAndBuildSendData(signalName, value);
            if (result.HasValue)
            {
                return result;
            }
        }
        return null;
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public CanDeviceStatistics GetStatistics()
    {
        var totalSignalCount = BmsRuntimes.Sum(b => b.MessageRuntimes.Values.Sum(m => m.Signals.Length));
        var onlineSignalCount = BmsRuntimes.Sum(b => b.MessageRuntimes.Values.Sum(m => m.SignalOnlineStatus.Count(s => s)));
        var totalMessageCount = BmsRuntimes.Sum(b => b.MessageRuntimes.Count);
        var onlineMessageCount = BmsRuntimes.Sum(b => b.MessageRuntimes.Values.Count(m => m.IsOnline));

        var stats = new CanDeviceStatistics
        {
            DeviceId = DeviceId,
            DeviceName = DeviceName,
            DeviceStatus = DeviceStatus,
            LastCommunicationTime = LastCommunicationTime,
            TotalBmsCount = BmsRuntimes.Count,
            OnlineBmsCount = BmsRuntimes.Count(b => b.IsOnline),
            TotalMessageCount = totalMessageCount,
            OnlineMessageCount = onlineMessageCount,
            TotalVariableCount = totalSignalCount,
            OnlineVariableCount = onlineSignalCount,
            TotalReceiveCount = BmsRuntimes.Sum(b => b.ReceiveCount),
            SendEnabledBmsCount = SendEnabledBms.Count
        };

        return stats;
    }

    /// <summary>
    /// 构建发送数据
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值</param>
    /// <returns>CAN数据</returns>
    public byte[]? BuildSendData(uint canId, Dictionary<string, double> signalValues)
    {
        foreach (var bmsRuntime in SendEnabledBms)
        {
            var data = bmsRuntime.BuildSendData(canId, signalValues);
            if (data != null)
            {
                return data;
            }
        }
        return null;
    }

    /// <summary>
    /// 检查设备连接状态
    /// </summary>
    /// <param name="timeoutSeconds">超时时间（秒）</param>
    public void CheckConnectionStatus(int timeoutSeconds = 10)
    {
        var now = DateTime.Now;
        var timeout = TimeSpan.FromSeconds(timeoutSeconds);

        if (now - LastCommunicationTime > timeout)
        {
            DeviceStatus = DeviceStatusEnum.OffLine;

                    // 设置所有BMS为离线
        foreach (var bmsRuntime in BmsRuntimes)
        {
            bmsRuntime.IsOnline = false;

            // 设置所有信号为离线
            foreach (var messageRuntime in bmsRuntime.MessageRuntimes.Values)
            {
                for (int i = 0; i < messageRuntime.SignalOnlineStatus.Length; i++)
                {
                    messageRuntime.SignalOnlineStatus[i] = false;
                }
            }
        }
        }
    }
}

/// <summary>
/// CAN设备统计信息
/// </summary>
public class CanDeviceStatistics
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatusEnum DeviceStatus { get; set; }

    /// <summary>
    /// 最后通讯时间
    /// </summary>
    public DateTime LastCommunicationTime { get; set; }

    /// <summary>
    /// 总BMS数量
    /// </summary>
    public int TotalBmsCount { get; set; }

    /// <summary>
    /// 在线BMS数量
    /// </summary>
    public int OnlineBmsCount { get; set; }

    /// <summary>
    /// 总报文数量
    /// </summary>
    public int TotalMessageCount { get; set; }

    /// <summary>
    /// 在线报文数量
    /// </summary>
    public int OnlineMessageCount { get; set; }

    /// <summary>
    /// 总变量数量
    /// </summary>
    public int TotalVariableCount { get; set; }

    /// <summary>
    /// 在线变量数量
    /// </summary>
    public int OnlineVariableCount { get; set; }

    /// <summary>
    /// 总接收次数
    /// </summary>
    public long TotalReceiveCount { get; set; }

    /// <summary>
    /// 启用发送的BMS数量
    /// </summary>
    public int SendEnabledBmsCount { get; set; }

    /// <summary>
    /// BMS在线率
    /// </summary>
    public double BmsOnlineRate => TotalBmsCount > 0 ? (double)OnlineBmsCount / TotalBmsCount : 0;

    /// <summary>
    /// 报文在线率
    /// </summary>
    public double MessageOnlineRate => TotalMessageCount > 0 ? (double)OnlineMessageCount / TotalMessageCount : 0;

    /// <summary>
    /// 变量在线率
    /// </summary>
    public double VariableOnlineRate => TotalVariableCount > 0 ? (double)OnlineVariableCount / TotalVariableCount : 0;

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        StopSendManager();
    }
}

using TouchSocket.Core;

namespace Matrix.Foundation;

/// <summary>
/// CAN发送接口
/// </summary>
public interface ICanSender
{
    /// <summary>
    /// 发送CAN报文（字节数据方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">字节数据</param>
    /// <returns>发送结果</returns>
    Task<OperResult> SendCanFrameAsync(uint canId, byte[] data);

    /// <summary>
    /// 发送CAN报文（信号值方式）
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="signalValues">信号值字典（信号名 -> 信号值）</param>
    /// <returns>发送结果</returns>
    Task<OperResult> SendCanSignalAsync(uint canId, Dictionary<string, double> signalValues);
}

/// <summary>
/// CAN发送数据结构
/// </summary>
public class CanSendData
{
    /// <summary>
    /// CAN ID
    /// </summary>
    public uint CanId { get; set; }

    /// <summary>
    /// 字节数据
    /// </summary>
    public byte[] Data { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 是否为扩展帧
    /// </summary>
    public bool IsExtended { get; set; }

    /// <summary>
    /// 是否为远程帧
    /// </summary>
    public bool IsRemote { get; set; }

    /// <summary>
    /// 是否为CAN FD帧
    /// </summary>
    public bool IsCanFd { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public CanSendData()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="canId">CAN ID</param>
    /// <param name="data">数据</param>
    public CanSendData(uint canId, byte[] data)
    {
        CanId = canId;
        Data = data;
        IsExtended = canId > 0x7FF; // 自动判断是否为扩展帧
    }

    /// <summary>
    /// 转换为CAN帧
    /// </summary>
    /// <returns>CAN帧</returns>
    public CanFrame ToCanFrame()
    {
        return new CanFrame
        {
            Id = CanId,
            Data = Data,
            IsExtended = IsExtended,
            IsRemote = IsRemote,
            IsCanFd = IsCanFd
        };
    }
}

/// <summary>
/// CAN插件发送请求格式
/// </summary>
public class CanPluginSendRequest
{
    /// <summary>
    /// 发送类型
    /// </summary>
    public CanSendRequestType RequestType { get; set; }

    /// <summary>
    /// CAN ID
    /// </summary>
    public uint CanId { get; set; }

    /// <summary>
    /// 字节数据（当RequestType为ByteData时使用）
    /// </summary>
    public byte[]? ByteData { get; set; }

    /// <summary>
    /// 信号值数据（当RequestType为SignalData时使用）
    /// </summary>
    public Dictionary<string, double>? SignalData { get; set; }

    /// <summary>
    /// 解析发送请求字符串
    /// 格式1（字节数据）：0x1800F401,00 01 02 03 04 05 06 07
    /// 格式2（信号数据）：0x1800F401,Signal1=123.45,Signal2=67.89
    /// </summary>
    /// <param name="requestString">请求字符串</param>
    /// <returns>发送请求对象</returns>
    public static CanPluginSendRequest Parse(string requestString)
    {
        if (string.IsNullOrWhiteSpace(requestString))
            throw new ArgumentException("发送请求字符串不能为空", nameof(requestString));

        var parts = requestString.Split(',', 2);
        if (parts.Length != 2)
            throw new ArgumentException("发送请求格式错误，应为：CAN_ID,DATA", nameof(requestString));

        // 解析CAN ID
        var canIdStr = parts[0].Trim();
        if (!canIdStr.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            throw new ArgumentException("CAN ID必须以0x开头", nameof(requestString));

        if (!uint.TryParse(canIdStr[2..], System.Globalization.NumberStyles.HexNumber, null, out var canId))
            throw new ArgumentException("CAN ID格式错误", nameof(requestString));

        var dataStr = parts[1].Trim();
        var request = new CanPluginSendRequest { CanId = canId };

        // 判断数据类型
        if (dataStr.Contains('='))
        {
            // 信号数据格式
            request.RequestType = CanSendRequestType.SignalData;
            request.SignalData = new Dictionary<string, double>();

            var signalPairs = dataStr.Split(',');
            foreach (var pair in signalPairs)
            {
                var signalParts = pair.Split('=', 2);
                if (signalParts.Length == 2)
                {
                    var signalName = signalParts[0].Trim();
                    if (double.TryParse(signalParts[1].Trim(), out var signalValue))
                    {
                        request.SignalData[signalName] = signalValue;
                    }
                }
            }
        }
        else
        {
            // 字节数据格式
            request.RequestType = CanSendRequestType.ByteData;
            var byteStrings = dataStr.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var bytes = new List<byte>();

            foreach (var byteStr in byteStrings)
            {
                if (byte.TryParse(byteStr, System.Globalization.NumberStyles.HexNumber, null, out var b))
                {
                    bytes.Add(b);
                }
            }

            request.ByteData = bytes.ToArray();
        }

        return request;
    }
}

/// <summary>
/// CAN发送请求类型
/// </summary>
public enum CanSendRequestType
{
    /// <summary>
    /// 字节数据
    /// </summary>
    ByteData,

    /// <summary>
    /// 信号数据
    /// </summary>
    SignalData
}
